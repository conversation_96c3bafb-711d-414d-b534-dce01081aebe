package com.trs.moye.batch.engine.controller;

import com.trs.ai.moye.schedule.starter.xxljob.XXLJobManager;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.monitor.entity.spark.SparkResourceStatus;
import com.trs.moye.batch.engine.config.ExecutorConfig;
import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.entity.BatchTaskTracer;
import com.trs.moye.batch.engine.entity.vo.BatchTaskVO;
import com.trs.moye.batch.engine.entity.vo.ExecuteResultMap;
import com.trs.moye.batch.engine.entity.vo.ExecuteResultRequest;
import com.trs.moye.batch.engine.enums.TriggerModeEnum;
import com.trs.moye.batch.engine.service.BatchTaskMonitor;
import com.trs.moye.batch.engine.service.BatchTaskService;
import com.trs.moye.batch.engine.service.SparkLogService;
import com.trs.moye.batch.engine.service.SparkQueryService;
import com.trs.moye.batch.engine.spark.SparkApplication;
import com.trs.moye.batch.engine.spark.SparkTaskManager;
import com.trs.moye.batch.engine.task.LogFileWriter;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutorService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 批处理相关接口
 *
 * <AUTHOR>
 * @since 2024/06/19
 */
@RestController
@RequestMapping("/task")
@Slf4j
public class BatchTaskController {

    @Resource(name = ExecutorConfig.API_TASK_EXECUTOR)
    private ExecutorService apiTaskExecutor;

    @Resource
    BatchTaskService batchTaskService;

    @Resource
    SparkTaskManager sparkTaskManager;

    @Resource
    BatchTaskRecordMapper batchTaskRecordMapper;

    @Resource
    XXLJobManager xxlJobManager;

    @Resource
    LogFileWriter logFileWriter;

    @Resource
    SparkLogService sparkLogService;

    @Resource
    SparkQueryService sparkQueryService;

    /**
     * 执行任务
     *
     * @param tasks 批处理任务传输类
     * <AUTHOR>
     */
    @PostMapping("/execute")
    public void execute(@RequestBody List<BatchTaskVO> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            log.warn("tasks is empty");
            throw new BizException("子任务列表为空");
        }

        // 产生日志记录
        BatchTaskMonitor.insertOrUpdateRecord(BatchTaskRecord.beginTask(tasks.get(0),
            TriggerModeEnum.IMMEDIATE, LocalDateTime.now()));

        // 执行任务
        apiTaskExecutor.submit(() -> batchTaskService.executeCodeTasks(tasks, true));
    }

    /**
     * 执行任务(同步)
     *
     * @param tasks 批处理任务传输类
     * @return ResponseMessage
     * <AUTHOR>
     */
    @PostMapping("/execute/sync")
    public ResponseMessage executeSync(@RequestBody List<BatchTaskVO> tasks) {
        // 产生日志记录
        BatchTaskMonitor.insertOrUpdateRecord(BatchTaskRecord.beginTask(tasks.get(0),
            TriggerModeEnum.IMMEDIATE, LocalDateTime.now()));

        // 执行任务
        return batchTaskService.executeCodeTasks(tasks, false);
    }

    /**
     * 立即执行dag任务
     *
     * @param task 批处理任务传输类
     */
    @PostMapping("/dag/execute")
    public void dagExecute(@RequestBody BatchTaskVO task) {
        log.info("executeJavaTaskAsync task: {}", JsonUtils.toJsonString(task));

        // 产生日志记录
        BatchTaskMonitor.insertOrUpdateRecord(
            BatchTaskRecord.beginTask(task, TriggerModeEnum.IMMEDIATE, LocalDateTime.now()));

        apiTaskExecutor.execute(() -> batchTaskService.executeDagTask(task, true));
    }

    /**
     * 测试dag任务
     *
     * @param task 批处理任务传输类
     * @return 任务执行记录
     */
    @PostMapping("/dag/test")
    public List<BatchTaskTracer> dagTest(@RequestBody BatchTaskVO task) {
        log.info("testDagTask task: {}", JsonUtils.toJsonString(task));
        return batchTaskService.testDagTask(task);
    }

    /**
     * 立即执行dag任务（同步）
     *
     * @param task 批处理任务传输类
     * @return ResponseMessage
     */
    @PostMapping("/dag/execute/sync")
    public ResponseMessage dagExecuteSync(@RequestBody BatchTaskVO task) {
        BatchTaskMonitor.insertOrUpdateRecord(
            BatchTaskRecord.beginTask(task, TriggerModeEnum.IMMEDIATE, LocalDateTime.now()));
        return batchTaskService.executeDagTask(task, false);
    }


    /**
     * 获取执行结果
     *
     * @param executeResultRequest 参数
     * @return 执行结果
     */
    @PostMapping("/execute/result")
    public List<ExecuteResultMap> executeResult(@RequestBody ExecuteResultRequest executeResultRequest) {
        log.info("获取请求执行结果参数:{}", executeResultRequest);
        return batchTaskService.executeResult(executeResultRequest);
    }


    /**
     * 停止spark任务
     *
     * @param executeId 执行 id
     */
    @PostMapping("/kill/{executeId}")
    public void kill(@PathVariable("executeId") String executeId) {
        BatchTaskRecord batchTaskRecord = batchTaskRecordMapper.getByExecuteId(executeId);
        if (batchTaskRecord.getEndTime() != null) {
            throw new BizException("任务已结束");
        }

        if (batchTaskRecord.getRetryXxlJobId() != null) {
            log.info(
                "batch task record [executeId:{}] 存在重试xxl-job任务 [xxlJobId:{}]，停止任务同时删除xxl-job重试任务",
                batchTaskRecord.getExecuteId(), batchTaskRecord.getRetryXxlJobId());
            xxlJobManager.deleteJob(batchTaskRecord.getRetryXxlJobId());
        }
        sparkTaskManager.killApp(executeId);

        // 更新任务状态为已结束
        BatchTaskMonitor.insertOrUpdateRecord(BatchTaskRecord.killTask(executeId, LocalDateTime.now()));
        BatchTaskMonitor.insertTracer(BatchTaskTracer.killTask(executeId, LocalDateTime.now()));
    }

    /**
     * 刷新日志
     *
     * @param executeId 执行id
     */
    @PostMapping("/log/flush/{executeId}")
    public void flush(@PathVariable("executeId") String executeId) {
        logFileWriter.flush(executeId);
        SparkApplication sparkApplication = sparkTaskManager.getApplication(executeId);
        if (sparkApplication != null) {
            sparkLogService.storageStandaloneLog(sparkApplication);
        }
    }

    /**
     * 获取Spark集群资源状态
     *
     * @param principal  Kerberos认证principal
     * @param keytabPath Kerberos认证keytab文件路径
     * @return {@link ResponseMessage }
     * <AUTHOR>
     * @since 2025/09/03 16:58:49
     */
    @GetMapping("/cluster/resource/status")
    public ResponseMessage getClusterResourceStatus(@RequestParam(required = false) String principal,
        @RequestParam(required = false) String keytabPath) {

        log.info("获取Spark集群资源状态 [authProvided:{}]",
            StringUtils.isNotEmpty(principal) && StringUtils.isNotEmpty(keytabPath));

        try {
            // 获取集群资源状态
            SparkResourceStatus resourceStatus = sparkQueryService.getClusterResourceStatus(principal, keytabPath);

            log.info(
                "成功获取Spark集群资源状态 [clusterMode:{}, clusterStatus:{}, totalCpuCores:{}, usedCpuCores:{}, totalMemory:{}, usedMemory:{}]",
                resourceStatus.getMode(), resourceStatus.getState(), resourceStatus.getTotalCpuCores(),
                resourceStatus.getUsedCpuCores(),
                resourceStatus.getTotalMemory(), resourceStatus.getUsedMemory());
            log.debug("调用ResponseMessage.ok方法，参数类型: {}", resourceStatus.getClass().getName());
            return ResponseMessage.ok(resourceStatus);
        } catch (Exception e) {
            log.error("获取Spark集群资源状态异常", e);
            log.debug("调用ResponseMessage.error方法，错误信息: {}", "系统异常: " + e.getMessage());
            return ResponseMessage.error("系统异常: " + e.getMessage());
        }
    }
}


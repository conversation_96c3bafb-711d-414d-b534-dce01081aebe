package com.trs.moye.batch.engine.utils;

import com.trs.moye.base.common.enums.Mode;
import com.trs.moye.base.monitor.entity.spark.SparkResourceStatus;
import com.trs.moye.base.monitor.enums.ClusterState;
import com.trs.moye.batch.engine.entity.dto.BaseResourceInfo;
import com.trs.moye.batch.engine.entity.dto.StandaloneResourceInfo;
import com.trs.moye.batch.engine.entity.dto.YarnResourceInfo;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;

/**
 * 资源数据转换工具类 - 用于数据模型之间的转换
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@Slf4j
public class ResourceDataConverter {

    /**
     * 内存单位转换常量：MB到GB
     */
    private static final double MB_TO_GB = 1024.0;

    /**
     * 内存单位转换常量：GB到TB
     */
    private static final double GB_TO_TB = 1024.0;

    /**
     * 将基础资源信息转换为集群资源状态
     *
     * @param baseResourceInfo 基础资源信息
     * @return 集群资源状态
     * @throws IllegalArgumentException 参数无效异常
     */
    public static SparkResourceStatus convertFromBaseResource(BaseResourceInfo baseResourceInfo) {
        log.debug("开始转换基础资源信息");

        // 参数验证
        if (baseResourceInfo == null) {
            throw new IllegalArgumentException("基础资源信息不能为null");
        }

        try {
            SparkResourceStatus status = new SparkResourceStatus();
            status.setTotalCpuCores(baseResourceInfo.getTotalCpuCores());
            status.setUsedCpuCores(baseResourceInfo.getUsedCpuCores());
            status.setTotalMemory(formatMemoryWithUnit(baseResourceInfo.getTotalMemoryMb()));
            status.setUsedMemory(formatMemoryWithUnit(baseResourceInfo.getUsedMemoryMb()));
            status.setQueryTime(LocalDateTime.now());
            status.setState(
                baseResourceInfo.getClusterState() != null ? baseResourceInfo.getClusterState() : ClusterState.UNKNOWN);
            status.setMode(
                baseResourceInfo.getClusterMode() != null ? baseResourceInfo.getClusterMode() : Mode.LOCAL);

            // 设置活跃实体信息
            setActiveEntityInfo(status, baseResourceInfo);

            log.debug("成功转换基础资源信息 [clusterMode:{}, clusterState:{}]",
                baseResourceInfo.getClusterMode(), baseResourceInfo.getClusterState());
            return status;
        } catch (Exception e) {
            log.error("转换基础资源信息失败", e);
            throw new RuntimeException("转换基础资源信息失败", e);
        }
    }


    /**
     * 格式化内存大小并添加适当的单位
     *
     * @param memoryMB 内存大小(MB)
     * @return 带单位的内存字符串
     */
    public static String formatMemoryWithUnit(long memoryMB) {
        if (memoryMB == 0) {
            return "0 MB";
        }

        // 转换为TB
        double memoryTb = memoryMB / (MB_TO_GB * GB_TO_TB);
        if (memoryTb >= 1) {
            return String.format("%.2f TB", memoryTb);
        }

        // 转换为GB
        double memoryGb = memoryMB / MB_TO_GB;
        if (memoryGb >= 1) {
            return String.format("%.2f GB", memoryGb);
        }

        // 保持MB单位
        return String.format("%d MB", memoryMB);
    }

    /**
     * 设置活跃worker数信息
     *
     * @param status           集群资源状态
     * @param baseResourceInfo 基础资源信息
     */
    private static void setActiveEntityInfo(SparkResourceStatus status, BaseResourceInfo baseResourceInfo) {
        try {
            // 根据集群模式设置活跃worker数信息
            if (baseResourceInfo.getClusterMode() == Mode.STANDALONE
                && baseResourceInfo instanceof StandaloneResourceInfo) {
                StandaloneResourceInfo standaloneInfo = (StandaloneResourceInfo) baseResourceInfo;
                status.setActiveWorkers(standaloneInfo.getActiveWorkers());
            } else if (baseResourceInfo.getClusterMode() == Mode.YARN && baseResourceInfo instanceof YarnResourceInfo) {
                YarnResourceInfo yarnInfo = (YarnResourceInfo) baseResourceInfo;
                status.setActiveWorkers(yarnInfo.getActiveNodes());
            } else {
                status.setActiveWorkers(0);
            }
        } catch (Exception e) {
            log.warn("设置活跃worker数信息时发生异常", e);
            status.setActiveWorkers(0);
        }
    }
}
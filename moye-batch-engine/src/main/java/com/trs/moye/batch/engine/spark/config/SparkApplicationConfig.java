package com.trs.moye.batch.engine.spark.config;

import com.trs.moye.base.common.enums.Mode;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

/**
 * spark 任务参数配置
 */
@Data
@Validated
@Configuration
@ConfigurationProperties(prefix = "spark")
public class SparkApplicationConfig {

    /**
     * spark 目录
     */
    @NotBlank(message = "spark home 不能为空")
    private String sparkHome;

    /**
     * master 是一个 Spark 的 cluster URL，或者指定为在 local mode（本地模式）中运行的 “local” 字符串，或者 YARN 模式的 “yarn” 字符串 <br/> e.g. yarn,
     * local, spark://master-ip:port
     */
    @NotBlank(message = "master 不能为空")
    private String master;

    /**
     * spark提交任务的模式
     *
     * @return 模式
     */
    public Mode getMode() {
        return Mode.getMode(master);
    }

    /**
     * cluster 或者 client
     */
    private String deployMode;

    /**
     * 额外jar包所在目录
     */
    private String jarsDirectory;

    /**
     * 额外文件目录
     */
    private String fileDirectory;

    /**
     * 额外python依赖目录
     */
    private String pyFileDirectory;

    /**
     * java主类名称
     */
    private String javaMainClass;

    /**
     * hive路径
     */
    private String hiveMetastoreUris;

    /**
     * java包路径
     */
    private String javaJarPath;

    /**
     * driver ip
     */
    private String driverHost;

    /**
     * driver port
     */
    private String driverPort;

    /**
     * block manager port
     */
    private String blockManagerPort;

    /**
     * port max retries
     */
    private String portMaxRetries;

    /**
     * spark web ui e.g. http://**************:8080/
     */
    private String webUiUrl;

    /**
     * spark额外配置
     */
    private Map<String, String> appConf;

}

package com.trs.moye.batch.engine.fetcher;

import com.trs.moye.batch.engine.entity.dto.BaseResourceInfo;
import java.io.IOException;

/**
 * 资源获取器统一接口 - 定义获取集群资源状态的通用方法
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
public interface ResourceFetcher {

    /**
     * 获取资源状态
     *
     * @param principal  认证主体
     * @param keytabPath 密钥表路径
     * @return 资源信息
     * @throws IOException          ioexception
     * @throws InterruptedException 中断异常
     * <AUTHOR>
     * @since 2025/09/03 17:01:10
     */
    BaseResourceInfo getResourceStatus(String principal, String keytabPath) throws IOException, InterruptedException;
}
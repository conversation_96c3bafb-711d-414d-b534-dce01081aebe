package com.trs.moye.batch.engine.entity.dto;

import com.trs.moye.base.common.enums.Mode;
import com.trs.moye.base.monitor.enums.ClusterState;
import lombok.Data;

/**
 * 基础资源信息 - 包含所有集群类型的共同属性
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@Data
public abstract class BaseResourceInfo {

    /**
     * 总CPU核心数
     */
    private int totalCpuCores;

    /**
     * 已使用CPU核心数
     */
    private int usedCpuCores;

    /**
     * 总内存(MB)
     */
    private long totalMemoryMb;

    /**
     * 已使用内存(MB)
     */
    private long usedMemoryMb;

    /**
     * 集群状态
     */
    private ClusterState clusterState;

    /**
     * 获取集群模式
     *
     * @return 集群模式
     */
    public abstract Mode getClusterMode();

    /**
     * 获取活跃实体数量描述
     *
     * @return 活跃实体数量描述
     */
    public abstract String getActiveEntityDescription();
}
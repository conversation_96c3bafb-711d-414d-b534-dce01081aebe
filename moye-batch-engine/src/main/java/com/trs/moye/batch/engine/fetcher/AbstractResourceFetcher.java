package com.trs.moye.batch.engine.fetcher;

import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.batch.engine.entity.dto.BaseResourceInfo;
import com.trs.moye.batch.engine.utils.RetryUtils;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象资源获取器 - 提供资源获取的通用逻辑
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@Slf4j
public abstract class AbstractResourceFetcher implements ResourceFetcher {

    /**
     * 获取集群资源状态的通用方法，包含重试逻辑
     *
     * @param fetcherName      获取器名称
     * @param resourceSupplier 资源供应者
     * @return 基础资源信息
     * @throws BizException 资源获取异常
     */
    protected BaseResourceInfo fetchResourceWithRetry(String fetcherName, ResourceSupplier resourceSupplier)
        throws IOException, InterruptedException {

        log.info("[{}] 开始获取集群资源状态", fetcherName);

        return RetryUtils.executeWithDefaultRetry(fetcherName + "集群资源状态查询", () -> {
            try {
                BaseResourceInfo resourceInfo = resourceSupplier.get();
                log.debug("[{}] 成功获取集群资源状态 [clusterMode:{}, clusterState:{}]",
                    fetcherName, resourceInfo.getClusterMode(), resourceInfo.getClusterState());
                return resourceInfo;
            } catch (Exception e) {
                log.error("[{}] 获取集群资源状态时发生异常", fetcherName, e);
                throw wrapException(fetcherName, e);
            }
        });
    }

    /**
     * 包装异常为业务异常
     *
     * @param fetcherName 获取器名称
     * @param e           原始异常
     * @return 业务异常
     * <AUTHOR>
     * @since 2025/09/03 16:56:18
     */
    protected BizException wrapException(String fetcherName, Exception e) {
        if (e instanceof BizException) {
            return (BizException) e;
        } else if (e instanceof IOException) {
            return new BizException(fetcherName + "集群连接失败: " + e.getMessage(), e);
        } else if (e instanceof InterruptedException) {
            return new BizException(fetcherName + "集群查询被中断: " + e.getMessage(), e);
        } else {
            return new BizException(fetcherName + "集群资源状态查询异常: " + e.getMessage(), e);
        }
    }

    /**
     * 资源供应者函数式接口
     */
    @FunctionalInterface
    protected interface ResourceSupplier {

        BaseResourceInfo get() throws Exception;
    }
}
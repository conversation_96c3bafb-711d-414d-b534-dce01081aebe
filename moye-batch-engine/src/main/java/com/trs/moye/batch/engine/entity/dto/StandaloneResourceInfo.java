package com.trs.moye.batch.engine.entity.dto;

import com.trs.moye.base.common.enums.Mode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Standalone资源信息
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StandaloneResourceInfo extends BaseResourceInfo {

    /**
     * 活跃工作节点数
     */
    private int activeWorkers;

    /**
     * 获取集群模式
     *
     * @return 集群模式
     */
    @Override
    public Mode getClusterMode() {
        return Mode.STANDALONE;
    }

    /**
     * 获取活跃实体数量描述
     *
     * @return 活跃实体数量描述
     */
    @Override
    public String getActiveEntityDescription() {
        return "活跃工作节点数: " + activeWorkers;
    }
}
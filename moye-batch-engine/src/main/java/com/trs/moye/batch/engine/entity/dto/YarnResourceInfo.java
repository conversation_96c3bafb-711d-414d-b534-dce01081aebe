package com.trs.moye.batch.engine.entity.dto;

import com.trs.moye.base.common.enums.Mode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Yarn资源信息
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class YarnResourceInfo extends BaseResourceInfo {

    /**
     * 活跃节点数
     */
    private int activeNodes;

    /**
     * 获取集群模式
     *
     * @return 集群模式
     */
    @Override
    public Mode getClusterMode() {
        return Mode.YARN;
    }

    /**
     * 获取活跃实体数量描述
     *
     * @return 活跃实体数量描述
     */
    @Override
    public String getActiveEntityDescription() {
        return "活跃节点数: " + activeNodes;
    }
}
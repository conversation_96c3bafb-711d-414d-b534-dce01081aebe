package com.trs.moye.batch.engine.utils;

import com.trs.moye.base.data.connection.dao.AuthCertificateMapper;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.batch.engine.config.HadoopProperties;
import java.io.File;
import java.io.IOException;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.security.UserGroupInformation;

/**
 * Kerberos认证工具类 - 统一处理Kerberos认证相关的逻辑 消除ClusterQueryService和TaskRecoveryService中的代码重复
 *
 * <AUTHOR>
 * @since 2025/08/31
 */
@Slf4j
public class KerberosAuthUtils {

    /**
     * 构建Hadoop配置资源集合
     *
     * @param hadoopProperties Hadoop配置属性
     * @return 配置资源集合
     */
    public static Set<String> buildHadoopResources(HadoopProperties hadoopProperties) {
        Set<String> resources = new HashSet<>(3);
        resources.add(hadoopProperties.getCoreSiteXmlPath());
        resources.add(hadoopProperties.getHdfsSiteXmlPath());
        resources.add(hadoopProperties.getYarnSiteXmlPath());
        log.debug("已构建Hadoop配置资源: {}", resources);
        return resources;
    }

    /**
     * 创建Hadoop Configuration对象
     *
     * @param hadoopProperties Hadoop配置属性
     * @return Configuration对象
     */
    public static Configuration createHadoopConfiguration(HadoopProperties hadoopProperties) {
        Set<String> resources = buildHadoopResources(hadoopProperties);
        Configuration conf = new Configuration();

        // 添加配置资源
        for (String resource : resources) {
            conf.addResource(new Path(resource));
        }

        return conf;
    }

    /**
     * 执行Kerberos认证
     *
     * @param conf       Hadoop配置
     * @param principal  Kerberos认证principal
     * @param keytabPath Kerberos认证keytab文件路径
     * @throws IOException 认证异常
     */
    public static void authenticateWithKerberos(Configuration conf, String principal, String keytabPath)
        throws IOException {
        if (Objects.isNull(principal) || Objects.isNull(keytabPath)) {
            log.debug("Kerberos认证参数不完整，跳过认证");
            return;
        }

        log.debug("执行Kerberos认证 [principal:{}]", principal);

        // 验证keytab文件存在性
        File keytabFile = new File(keytabPath);
        if (!keytabFile.exists()) {
            throw new IOException("Keytab文件不存在: " + keytabPath);
        }

        // 设置UserGroupInformation配置并执行Kerberos认证
        UserGroupInformation.setConfiguration(conf);
        UserGroupInformation.loginUserFromKeytab(principal, keytabPath);

        log.debug("Kerberos认证成功 [principal:{}]", principal);
    }

    /**
     * 从已查询的执行配置中提取Kerberos证书 避免重复数据库查询
     *
     * @param executeConfig         数据模型执行配置
     * @param authCertificateMapper 认证证书映射器
     * @return Kerberos证书实体，如果没有配置则返回null
     */
    public static KerberosCertificate extractKerberosCertificateFromConfig(DataModelExecuteConfig executeConfig,
        AuthCertificateMapper authCertificateMapper) {
        try {
            if (Objects.isNull(executeConfig)) {
                log.debug("执行配置为空，无法提取Kerberos证书");
                return null;
            }

            BatchProcessSparkConfig sparkConfig = executeConfig.getSparkConfig();
            if (Objects.isNull(sparkConfig) || Objects.isNull(sparkConfig.getCertificateId())) {
                log.debug("未配置Spark配置或证书ID");
                return null;
            }

            // 获取KerberosCertificate
            KerberosCertificate kerberosCertificate = authCertificateMapper.selectById(sparkConfig.getCertificateId());
            if (Objects.isNull(kerberosCertificate)) {
                log.debug("未找到Kerberos证书 [certificateId:{}]", sparkConfig.getCertificateId());
                return null;
            }

            log.debug("成功从执行配置中提取Kerberos证书 [principal:{}]", kerberosCertificate.getPrincipal());

            return kerberosCertificate;

        } catch (Exception e) {
            log.warn("从执行配置中提取Kerberos证书时发生异常", e);
            return null;
        }
    }

    /**
     * 检查Kerberos认证参数是否有效
     *
     * @param principal  Kerberos认证principal
     * @param keytabPath Kerberos认证keytab文件路径
     * @return 是否有效
     */
    public static boolean isValidKerberosAuth(String principal, String keytabPath) {
        return Objects.nonNull(principal) && Objects.nonNull(keytabPath)
            && !principal.trim().isEmpty() && !keytabPath.trim().isEmpty();
    }
}
package com.trs.moye.batch.engine.fetcher;

import com.trs.moye.base.common.enums.Mode;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 资源获取器工厂类
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@Component
public class ResourceFetcherFactory {

    private final Map<Mode, ResourceFetcher> fetcherMap;

    /**
     * 资源获取器工厂
     *
     * @param yarnResourceFetcher       纱线资源获取器
     * @param standaloneResourceFetcher 独立资源获取器
     * <AUTHOR>
     * @since 2025/09/03 16:57:22
     */
    @Autowired
    public ResourceFetcherFactory(YarnResourceFetcher yarnResourceFetcher,
        StandaloneResourceFetcher standaloneResourceFetcher) {
        this.fetcherMap = new HashMap<>();
        this.fetcherMap.put(Mode.YARN, yarnResourceFetcher);
        this.fetcherMap.put(Mode.STANDALONE, standaloneResourceFetcher);
    }

    /**
     * 根据集群模式创建资源获取器
     *
     * @param clusterMode 集群模式
     * @return 资源获取器实例
     * <AUTHOR>
     * @since 2025/09/03 16:57:19
     */
    public ResourceFetcher createFetcher(Mode clusterMode) {
        if (clusterMode == null) {
            throw new IllegalArgumentException("集群模式不能为null");
        }

        ResourceFetcher fetcher = fetcherMap.get(clusterMode);
        if (fetcher == null) {
            throw new IllegalArgumentException("不支持的集群模式: " + clusterMode);
        }

        return fetcher;
    }

    /**
     * 注册新的资源获取器
     *
     * @param clusterMode 集群模式
     * @param fetcher     资源获取器
     */
    public void registerFetcher(Mode clusterMode, ResourceFetcher fetcher) {
        if (clusterMode == null || fetcher == null) {
            throw new IllegalArgumentException("集群模式和资源获取器不能为null");
        }

        fetcherMap.put(clusterMode, fetcher);
    }
}
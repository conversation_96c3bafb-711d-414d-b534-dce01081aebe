package com.trs.moye.batch.engine.fetcher;

import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.monitor.enums.ClusterState;
import com.trs.moye.batch.engine.entity.dto.BaseResourceInfo;
import com.trs.moye.batch.engine.entity.dto.StandaloneResourceInfo;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import com.trs.moye.batch.engine.utils.ResourceParsingUtils;
import java.io.IOException;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

/**
 * Standalone资源获取器 - 用于从Spark Standalone Master获取集群资源状态
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@Slf4j
@Component
public class StandaloneResourceFetcher extends AbstractResourceFetcher {

    @Resource
    private SparkApplicationConfig sparkApplicationConfig;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 获取Standalone集群资源状态
     *
     * @return StandaloneResourceInfo Standalone资源信息
     * @throws BizException 资源获取异常
     */
    public StandaloneResourceInfo getStandaloneResourceStatus() throws BizException, IOException, InterruptedException {

        String webUiUrl = sparkApplicationConfig.getWebUiUrl();
        if (StringUtils.isEmpty(webUiUrl)) {
            log.warn("[StandaloneResourceFetcher] Spark Web UI URL未配置，无法查询Standalone集群资源状态");
            throw new BizException("Spark Web UI URL未配置，请检查配置项spark.web-ui-url",
                "CONFIG_ERROR", "StandaloneResourceFetcher");
        }

        log.info("[StandaloneResourceFetcher] 开始获取Standalone集群资源状态 [webUiUrl:{}]", webUiUrl);
        return (StandaloneResourceInfo) fetchResourceWithRetry("StandaloneResourceFetcher", () -> {
            String clusterUrl = webUiUrl.replaceAll("/$", "") + "/";
            log.debug("[StandaloneResourceFetcher] 查询Standalone集群资源状态 [url:{}]", clusterUrl);

            try {
                // 发起HTTP GET请求获取集群首页
                ResponseEntity<String> response = restTemplate.getForEntity(clusterUrl, String.class);

                if (response.getStatusCode() != HttpStatus.OK) {
                    throw new BizException("HTTP请求失败，状态码: " + response.getStatusCode() +
                        ", URL: " + clusterUrl);
                }

                // 使用Jsoup解析HTML页面
                if (Objects.isNull(response.getBody())) {
                    throw new BizException("HTTP请求失败，响应体为空");
                }

                Document doc = Jsoup.parse(response.getBody());
                return parseStandaloneResourceInfo(doc, clusterUrl);

            } catch (HttpClientErrorException e) {
                log.error("[StandaloneResourceFetcher] HTTP客户端异常 [url:{}, status:{}]", clusterUrl,
                    e.getStatusCode(), e);
                throw new BizException("Standalone集群HTTP请求失败: " + e.getMessage(),
                    "HTTP_ERROR", "StandaloneResourceFetcher", e);
            } catch (ResourceAccessException e) {
                log.error("[StandaloneResourceFetcher] 网络连接异常 [url:{}]", clusterUrl, e);
                throw new BizException("Standalone集群连接失败，请检查网络和Web UI配置: " + e.getMessage(),
                    "NETWORK_ERROR", "StandaloneResourceFetcher", e);
            } catch (Exception e) {
                log.error("[StandaloneResourceFetcher] 查询Standalone集群资源状态时发生异常 [url:{}]", clusterUrl, e);
                throw new BizException("Standalone集群资源状态查询失败: " + e.getMessage(),
                    "UNKNOWN_ERROR", "StandaloneResourceFetcher", e);
            }
        });
    }

    /**
     * 获取Standalone集群资源状态（带认证参数版本）
     *
     * @param principal  认证主体
     * @param keytabPath 密钥表路径
     * @return BaseResourceInfo 基础资源信息
     * @throws BizException 资源获取异常
     */
    @Override
    public BaseResourceInfo getResourceStatus(String principal, String keytabPath)
        throws IOException, InterruptedException {
        log.info("[StandaloneResourceFetcher] 开始获取Standalone集群资源状态，principal: {}", principal);
        return getStandaloneResourceStatus();

    }

    /**
     * 解析Standalone资源信息
     *
     * @param doc HTML文档
     * @param url 请求URL
     * @return StandaloneResourceInfo Standalone资源信息
     */
    private StandaloneResourceInfo parseStandaloneResourceInfo(Document doc, String url) {
        StandaloneResourceInfo resourceInfo = new StandaloneResourceInfo();

        try {
            // 解析工作节点信息并设置活跃节点数
            int activeWorkers = parseActiveWorkers(doc);
            resourceInfo.setActiveWorkers(activeWorkers);

            // 解析集群摘要信息（从页面顶部的摘要部分）并直接设置到resourceInfo
            parseClusterSummary(doc, resourceInfo);

            // 设置集群状态
            resourceInfo.setClusterState(determineClusterState(activeWorkers, resourceInfo.getTotalCpuCores()));

            log.debug(
                "[StandaloneResourceFetcher] 成功解析Standalone资源信息 [totalCores:{}, usedCores:{}, totalMemoryMb:{}, usedMemoryMb:{}, activeWorkers:{}]",
                resourceInfo.getTotalCpuCores(), resourceInfo.getUsedCpuCores(), resourceInfo.getTotalMemoryMb(),
                resourceInfo.getUsedMemoryMb(), activeWorkers);

            return resourceInfo;

        } catch (Exception e) {
            log.error("[StandaloneResourceFetcher] 解析Standalone资源信息时发生异常 [url:{}]", url, e);
            throw new BizException("解析Standalone集群资源信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析集群摘要信息 从页面上方的集群摘要部分获取总资源和使用情况
     *
     * @param doc          HTML文档
     * @param resourceInfo 资源信息
     * <AUTHOR>
     * @since 2025/09/03 15:48:33
     */
    private void parseClusterSummary(Document doc, StandaloneResourceInfo resourceInfo) {
        // 查找集群摘要信息，通常在页面顶部的ul列表中
        Elements summaryItems = doc.select("ul li");
        boolean hasCoresInfo = false;
        boolean hasMemoryInfo = false;

        for (Element item : summaryItems) {
            String text = item.text().toLowerCase();

            // 解析CPU核心信息：如 "Cores in use: 12 Total, 0 Used"
            if (text.contains("cores in use:")) {
                parseCoresSummary(text, resourceInfo);
                hasCoresInfo = true;
            }

            // 解析内存信息：如 "Memory in use: 40.2 GiB Total, 0.0 B Used"
            if (text.contains("memory in use:")) {
                parseMemorySummary(text, resourceInfo);
                hasMemoryInfo = true;
            }
        }

        // 如果摘要信息不完整，尝试从表格中汇总缺失的信息
        if (!hasCoresInfo || !hasMemoryInfo || resourceInfo.getTotalCpuCores() == 0) {
            log.warn("[StandaloneResourceFetcher] 集群摘要信息不完整，将从Workers表格汇总缺失信息");
            summarizeFromWorkerTable(doc, resourceInfo, hasCoresInfo, hasMemoryInfo);
        }
    }

    /**
     * 解析CPU核心摘要信息
     *
     * @param text         发短信
     * @param resourceInfo 资源信息
     * <AUTHOR>
     * @since 2025/09/03 16:52:14
     */
    private void parseCoresSummary(String text, StandaloneResourceInfo resourceInfo) {
        try {
            // 匹配格式："cores in use: 12 total, 0 used"
            String[] parts = text.split(",");
            for (String part : parts) {
                String trimmedPart = part.trim().toLowerCase();
                if (trimmedPart.contains("total")) {
                    resourceInfo.setTotalCpuCores(extractNumber(trimmedPart));
                } else if (trimmedPart.contains("used")) {
                    resourceInfo.setUsedCpuCores(extractNumber(trimmedPart));
                }
            }
        } catch (Exception e) {
            log.warn("[StandaloneResourceFetcher] 解析CPU核心摘要失败 [text:{}]", text, e);
            // 解析失败时设置为默认值
            if (resourceInfo.getTotalCpuCores() == 0) {
                resourceInfo.setTotalCpuCores(0);
                resourceInfo.setUsedCpuCores(0);
            }
        }
    }

    /**
     * 解析内存摘要信息
     *
     * @param text         发短信
     * @param resourceInfo 资源信息
     * <AUTHOR>
     * @since 2025/09/03 16:52:18
     * @since 2025/09/03 16:52:18
     */
    private void parseMemorySummary(String text, StandaloneResourceInfo resourceInfo) {
        try {
            // 匹配格式："memory in use: 40.2 gib total, 0.0 b used"
            String[] parts = text.split(",");
            for (String part : parts) {
                String trimmedPart = part.trim().toLowerCase();
                if (trimmedPart.contains("total")) {
                    resourceInfo.setTotalMemoryMb(parseMemoryFromText(trimmedPart));
                } else if (trimmedPart.contains("used")) {
                    resourceInfo.setUsedMemoryMb(parseMemoryFromText(trimmedPart));
                }
            }
        } catch (Exception e) {
            log.warn("[StandaloneResourceFetcher] 解析内存摘要失败 [text:{}]", text, e);
            // 解析失败时设置为默认值
            if (resourceInfo.getTotalMemoryMb() == 0) {
                resourceInfo.setTotalMemoryMb(0);
                resourceInfo.setUsedMemoryMb(0);
            }
        }
    }

    /**
     * 从文本中提取数字
     *
     * @param text 文本
     * @return 整数值
     */
    private int extractNumber(final String text) {
        return ResourceParsingUtils.extractPositiveNumber(text);
    }

    /**
     * 从文本中解析内存值并转换为MB
     *
     * @param text 文本
     * @return 内存值（MB）
     */
    private long parseMemoryFromText(final String text) {
        return ResourceParsingUtils.parseMemory(text);
    }

    /**
     * 从Workers表格汇总资源信息
     *
     * @param doc           文本
     * @param resourceInfo  资源信息
     * @param hasCoresInfo  有核心信息
     * @param hasMemoryInfo 有内存信息
     */
    private void summarizeFromWorkerTable(Document doc, StandaloneResourceInfo resourceInfo,
        boolean hasCoresInfo, boolean hasMemoryInfo) {
        int totalCores = 0;
        long totalMemoryMb = 0;

        // 从表格中汇总总资源，但无法获取准确的使用情况
        Elements workerRows = doc.select("table tr:has(td)");

        for (Element row : workerRows) {
            Elements cells = row.select("td");
            // 状态列在第3列（索引2），核心数在第4列（索引3），内存在第5列（索引4）
            if (cells.size() >= 5 && "ALIVE".equalsIgnoreCase(cells.get(2).text().trim())) {
                totalCores += ResourceParsingUtils.parseInt(cells.get(3).text());
                totalMemoryMb += ResourceParsingUtils.parseMemory(cells.get(4).text());
            }
        }

        // 只设置缺失的信息，保留已从摘要中解析的信息
        if (!hasCoresInfo) {
            resourceInfo.setTotalCpuCores(totalCores);
            resourceInfo.setUsedCpuCores(0); // 使用资源需要从运行中的应用程序推算
        }

        if (!hasMemoryInfo) {
            resourceInfo.setTotalMemoryMb(totalMemoryMb);
            resourceInfo.setUsedMemoryMb(0); // 使用资源需要从运行中的应用程序推算
        }
    }

    /**
     * 解析活跃工作节点数量
     *
     * @param doc 医生
     * @return int
     * <AUTHOR>
     * @since 2025/09/03 15:57:30
     */
    private int parseActiveWorkers(Document doc) {
        int activeWorkers = 0;
        Elements workerRows = doc.select("table tr:has(td)");

        for (Element row : workerRows) {
            Elements cells = row.select("td");
            // 状态列在第3列（索引2）
            if (cells.size() >= 3) {
                String status = cells.get(2).text().trim();
                if ("ALIVE".equalsIgnoreCase(status)) {
                    activeWorkers++;
                }
            }
        }

        return activeWorkers;
    }

    /**
     * 确定集群状态
     *
     * @param activeWorkers 活跃工人
     * @param totalCores    总内核数
     * @return {@link ClusterState }
     * <AUTHOR>
     * @since 2025/09/03 15:57:32
     */
    private ClusterState determineClusterState(int activeWorkers, int totalCores) {
        if (activeWorkers == 0) {
            return ClusterState.STOPPED;
        } else if (totalCores > 0) {
            return ClusterState.ALIVE;
        } else {
            return ClusterState.ERROR;
        }
    }

}
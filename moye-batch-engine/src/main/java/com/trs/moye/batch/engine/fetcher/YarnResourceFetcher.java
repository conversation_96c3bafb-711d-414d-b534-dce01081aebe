package com.trs.moye.batch.engine.fetcher;

import com.trs.moye.base.monitor.enums.ClusterState;
import com.trs.moye.batch.engine.config.HadoopProperties;
import com.trs.moye.batch.engine.entity.dto.BaseResourceInfo;
import com.trs.moye.batch.engine.entity.dto.YarnResourceInfo;
import com.trs.moye.batch.engine.utils.KerberosAuthUtils;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.yarn.api.records.ApplicationReport;
import org.apache.hadoop.yarn.api.records.NodeReport;
import org.apache.hadoop.yarn.client.api.YarnClient;
import org.springframework.stereotype.Component;

/**
 * Yarn资源获取器 - 用于从Yarn ResourceManager获取集群资源状态
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@Slf4j
@Component
public class YarnResourceFetcher extends AbstractResourceFetcher {

    @Resource
    private HadoopProperties hadoopProperties;

    /**
     * 获取Yarn集群资源状态
     *
     * @param principal  Kerberos认证principal
     * @param keytabPath Kerberos认证keytab文件路径
     * @return YarnResourceInfo Yarn资源信息
     * <AUTHOR>
     * @since 2025/09/03 16:54:49
     */
    public YarnResourceInfo getYarnResourceStatus(String principal, String keytabPath)
        throws IOException, InterruptedException {

        log.info("[YarnResourceFetcher] 开始获取Yarn集群资源状态 [authProvided:{}]",
            KerberosAuthUtils.isValidKerberosAuth(principal, keytabPath));

        return (YarnResourceInfo) fetchResourceWithRetry("YarnResourceFetcher", () -> {
            YarnClient yarnClient = null;
            try {
                // 使用KerberosAuthUtils创建Hadoop配置
                Configuration conf = KerberosAuthUtils.createHadoopConfiguration(hadoopProperties);

                // 使用KerberosAuthUtils执行Kerberos认证
                if (KerberosAuthUtils.isValidKerberosAuth(principal, keytabPath)) {
                    KerberosAuthUtils.authenticateWithKerberos(conf, principal, keytabPath);
                }

                // 创建YarnClient
                yarnClient = YarnClient.createYarnClient();
                yarnClient.init(conf);
                yarnClient.start();

                // 获取集群节点信息
                List<NodeReport> nodeReports = yarnClient.getNodeReports();

                // 获取集群应用信息
                List<ApplicationReport> applications = yarnClient.getApplications();

                // 解析资源信息
                return parseYarnResourceInfo(nodeReports, applications);

            } catch (Exception e) {
                log.error("[YarnResourceFetcher] 获取Yarn集群资源状态时发生异常", e);
                throw wrapException("YarnResourceFetcher", e);
            } finally {
                if (Objects.nonNull(yarnClient)) {
                    try {
                        yarnClient.close();
                    } catch (Exception e) {
                        log.warn("[YarnResourceFetcher] 关闭YarnClient时发生异常", e);
                    }
                }
            }
        });
    }


    /**
     * 获取Yarn集群资源状态（带认证参数版本）
     *
     * @param principal  认证主体
     * @param keytabPath 密钥表路径
     * @return BaseResourceInfo 基础资源信息
     * <AUTHOR>
     * @since 2025/09/03 16:56:26
     */
    @Override
    public BaseResourceInfo getResourceStatus(String principal, String keytabPath)
        throws IOException, InterruptedException {
        log.info("[YarnResourceFetcher] 开始获取Yarn集群资源状态，principal: {}", principal);
        // 获取Yarn资源状态
        return getYarnResourceStatus(principal, keytabPath);

    }

    /**
     * 解析Yarn资源信息 - 改进版本，处理YARN API返回-1的情况
     *
     * @param nodeReports  节点报告列表
     * @param applications 应用报告列表
     * @return YarnResourceInfo Yarn资源信息
     */
    private YarnResourceInfo parseYarnResourceInfo(List<NodeReport> nodeReports, List<ApplicationReport> applications) {
        YarnResourceInfo resourceInfo = new YarnResourceInfo();

        // 计算总资源和活跃节点数
        int totalCpuCores = 0;
        long totalMemoryMb = 0;
        int activeNodes = 0;

        if (nodeReports != null) {
            for (NodeReport nodeReport : nodeReports) {
                if (nodeReport != null && nodeReport.getCapability() != null) {
                    org.apache.hadoop.yarn.api.records.Resource nodeResource = nodeReport.getCapability();
                    totalCpuCores += Math.max(0, nodeResource.getVirtualCores());
                    totalMemoryMb += Math.max(0L, nodeResource.getMemorySize());

                    // 统计活跃节点数
                    if (nodeReport.getNodeState() == org.apache.hadoop.yarn.api.records.NodeState.RUNNING) {
                        activeNodes++;
                    }
                }
            }
        }

        // 计算已使用资源 - 关键改进：处理YARN API返回-1的情况
        int usedCpuCores = 0;
        long usedMemoryMb = 0;
        int validApplications = 0;
        int invalidApplications = 0;

        if (applications != null) {
            for (ApplicationReport application : applications) {
                try {
                    if (application != null 
                        && application.getApplicationResourceUsageReport() != null) {
                        
                        // 获取资源使用报告
                        org.apache.hadoop.yarn.api.records.Resource usedResource = 
                            application.getApplicationResourceUsageReport().getUsedResources();
                        
                        if (usedResource != null) {
                            int appCpuCores = usedResource.getVirtualCores();
                            long appMemoryMb = usedResource.getMemorySize();
                            
                            // 关键检查：YARN API文档明确说明无效/不可访问报告会返回-1
                            // 我们需要跳过这些无效值，而不是累加它们
                            if (appCpuCores >= 0 && appMemoryMb >= 0) {
                                // 只有当值>=0时才累加，这样可以避免-1值的影响
                                usedCpuCores += appCpuCores;
                                usedMemoryMb += appMemoryMb;
                                validApplications++;
                            } else {
                                // 记录无效的应用资源报告
                                invalidApplications++;
                                log.debug("[YarnResourceFetcher] 跳过无效的应用资源报告 (CPU:{}, Memory:{}MB, 应用ID: {})", 
                                        appCpuCores, appMemoryMb, application.getApplicationId());
                            }
                        } else {
                            invalidApplications++;
                            log.debug("[YarnResourceFetcher] 应用资源使用报告为null (应用ID: {})", 
                                    application.getApplicationId());
                        }
                    } else {
                        invalidApplications++;
                    }
                } catch (Exception e) {
                    invalidApplications++;
                    log.warn("[YarnResourceFetcher] 获取应用资源使用情况时发生异常 (应用ID: {}): {}", 
                            application != null ? application.getApplicationId() : "unknown", e.getMessage());
                    // 继续处理其他应用，不因单个应用异常而中断
                }
            }
        }

        // 设置资源信息
        resourceInfo.setTotalCpuCores(totalCpuCores);
        resourceInfo.setUsedCpuCores(usedCpuCores);
        resourceInfo.setTotalMemoryMb(totalMemoryMb);
        resourceInfo.setUsedMemoryMb(usedMemoryMb);
        resourceInfo.setActiveNodes(activeNodes);

        // 设置集群状态
        if (nodeReports == null || nodeReports.isEmpty()) {
            resourceInfo.setClusterState(ClusterState.STOPPED);
        } else {
            resourceInfo.setClusterState(ClusterState.ALIVE);
        }

        log.info(
            "[YarnResourceFetcher] 成功解析Yarn资源信息 [totalCpuCores:{}, usedCpuCores:{}, totalMemoryMb:{}, usedMemoryMb:{}, activeNodes:{}, validApps:{}, invalidApps:{}]",
            totalCpuCores, usedCpuCores, totalMemoryMb, usedMemoryMb, activeNodes, validApplications, invalidApplications);

        return resourceInfo;
    }
}
{"permissions": {"allow": ["Read(/Users/<USER>/TRS/codeProjects/moye-v4/**)", "Bash(mvn checkstyle:check -q)", "Bash(mvn clean compile -q)", "Read(/Users/<USER>/TRS/codeProjects/moye-v4/moye-backend/src/main/java/com/trs/ai/moye/data/model/service/impl/**)", "Read(/Users/<USER>/TRS/codeProjects/moye-v4/moye-backend/src/main/java/com/trs/ai/moye/batchengine/service/impl/**)", "Read(/Users/<USER>/TRS/codeProjects/moye-v4/moye-backend/src/main/java/com/trs/ai/moye/batchengine/feign/**)", "Read(/Users/<USER>/TRS/codeProjects/moye-v4/**)", "Read(/Users/<USER>/TRS/codeProjects/moye-v4/**)", "Read(/Users/<USER>/TRS/codeProjects/moye-v4/**)", "WebSearch", "WebFetch(domain:hadoop.apache.org)"], "deny": [], "ask": []}}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.bigdata.dao.PoliceSituationAnalyseMapper">
    <select id="全区违法犯罪警情总数" resultType="java.lang.Integer">
        select COUNT(1)
        from dwd_gxgx_gx_zhzx_mrjq
        where bjsj between #{beginTime} and #{endTime}
          AND jqlb in ('刑事案件', '行政案件');
    </select>

    <select id="全区盗窃类警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND jqlx in ('盗窃', '盗窃（行政案件）')
    </select>

    <select id="全区诈骗类警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND jqlx in ('诈骗', '诈骗（行政案件）')
    </select>

    <select id="全区非侵财类警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND jqlx not in ('盗窃', '盗窃（行政案件）', '抢夺', '抢夺（行政案件）', '抢劫', '诈骗', '诈骗（行政案件）')
    </select>

    <select id="全区街面六类侵财警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND (jqlx in ('抢夺', '抢夺（行政案件）')
            or (jqlx in ('盗窃', '盗窃（行政案件）')
                and jqxl in ('扒窃（随窃）', '车内财物', '电动自行车', '摩托车', '汽车')
                   )
            )
    </select>

    <select id="全区殴打他人警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND jqlx = '殴打他人'
    </select>

    <select id="全区家暴警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND jqlx = '家暴'
    </select>

    <select id="龙湖时代天街" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND shfdzh LIKE '%龙湖时代天街%'
          AND jqlb in ('刑事案件', '行政案件');
    </select>

    <select id="富士康青年公寓" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND (shfdzh LIKE '%富士康%' or shfdzh LIKE '%青年公寓%')
          AND jqlb in ('刑事案件', '行政案件');
    </select>

    <select id="全区盗窃类警情高发时段" resultType="java.lang.Integer">
        select hour
        from (SELECT HOUR(bjsj) as hour,
                     count(1)   as count
              FROM dwd_gxgx_gx_zhzx_mrjq
              WHERE bjsj between #{beginTime} and #{endTime}
                AND jqlx in ('盗窃', '盗窃（行政案件）')
              GROUP BY hour
              ORDER BY count DESC
              LIMIT 1) as result;
    </select>

    <select id="家暴类警情部门统计" resultType="java.util.Map">
        select jzgx, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        where bjsj between #{beginTime} and #{endTime}
          AND jqlx = '家暴'
        group by jzgx
        order by count desc
    </select>

    <select id="盗窃类警情部门统计" resultType="java.util.Map">
        select jzgx, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        where bjsj between #{beginTime} and #{endTime}
          AND jqlx in ('盗窃', '盗窃（行政案件）')
        group by jzgx
        order by count desc
    </select>

    <select id="全区街面六类侵财警情部门统计" resultType="java.util.Map">
        select jzgx, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        where bjsj between #{beginTime} and #{endTime}
          AND (jqlx in ('抢夺', '抢夺（行政案件）')
            or (jqlx in ('盗窃', '盗窃（行政案件）')
                and jqxl in ('扒窃（随窃）', '车内财物', '电动自行车', '摩托车', '汽车')
                   )
            )
        group by jzgx
        order by count desc
    </select>

    <select id="全区街面六类侵财警情高发时间段" resultType="java.lang.Integer">
        select hour
        from (SELECT HOUR(bjsj) as hour,
                     count(1)   as count
              FROM dwd_gxgx_gx_zhzx_mrjq
              WHERE bjsj between #{beginTime} and #{endTime}
                AND (jqlx in ('抢夺', '抢夺（行政案件）')
                  or (jqlx in ('盗窃', '盗窃（行政案件）')
                      and jqxl in ('扒窃（随窃）', '车内财物', '电动自行车', '摩托车', '汽车')
                         )
                  )
              GROUP BY hour
              ORDER BY count DESC
              LIMIT 1) as result;
    </select>

    <select id="殴打他人警情部门统计" resultType="java.util.Map">
        select jzgx, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        where bjsj between #{beginTime} and #{endTime}
          AND jqlx = '殴打他人'
        group by jzgx
        order by count desc;
    </select>

    <select id="全区入室盗窃类警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND jqxl = '入户盗窃'
    </select>

    <select id="入室盗窃类警情部门统计" resultType="java.util.Map">
        select jzgx, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        where bjsj between #{beginTime} and #{endTime}
          AND jqxl = '入户盗窃'
        group by jzgx
        order by count desc;
    </select>

    <select id="全区电信诈骗类警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND jqxl = '通讯诈骗'
    </select>

    <select id="电信诈骗类警情部门统计" resultType="java.util.Map">
        select jzgx, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        where bjsj between #{beginTime} and #{endTime}
          AND jqxl = '通讯诈骗'
        group by jzgx
        order by count desc;
    </select>

    <select id="全区八类恶性犯罪警情数量" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM dwd_gxgx_gx_zhzx_mrjq
        WHERE bjsj between #{beginTime} and #{endTime}
          AND jqlx in ('杀人', '强奸', '故意伤害', '抢劫', '放火', '爆炸', '投放危险物质', '贩卖毒品')
    </select>

    <select id="盗窃类警情分类统计" resultType="java.util.Map">
        select '盗窃电动车' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '电动自行车'
        </where>
        union
        select '盗窃摩托车' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '摩托车'
        </where>
        union
        select '盗窃汽车' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '汽车'
        </where>
        union
        select '扒窃（随窃）' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '扒窃（随窃）'
        </where>
        union
        select '盗窃车内财物' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '车内财物'
        </where>
        union
        select '盗刷银行卡' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '盗刷银行卡'
        </where>
        union
        select '入室盗窃' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '入户盗窃'
        </where>
        union
        select '盗窃非机动车配件' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '非机动车配件'
        </where>
        union
        select '其他盗窃' as type, count(1) count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
            and jqlx in ('盗窃', '盗窃（行政案件）')
            and (jqxl is null or jqxl not in
            ('电动自行车', '摩托车', '汽车', '扒窃(随窃)', '车内财物', '盗刷银行卡', '入户盗窃',
            '非机动车配件'))
        </where>
    </select>

    <select id="街面六类侵财警情分类统计" resultType="java.util.Map">
        select '盗窃电动车' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '电动自行车'
        </where>
        union
        select '盗窃摩托车' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '摩托车'
        </where>
        union
        select '盗窃汽车' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '汽车'
        </where>
        union
        select '扒窃（随窃）' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '扒窃（随窃）'
        </where>
        union
        select '盗窃车内财物' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('盗窃', '盗窃（行政案件）')
              AND jqxl = '车内财物'
        </where>
        union
        select '抢劫' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx = '抢劫'
        </where>
        union
        select '抢夺' as type, count(1) as count
        from dwd_gxgx_gx_zhzx_mrjq
        <where>
            bjsj between #{beginTime} and #{endTime}
              AND jqlx in ('抢夺', '抢夺（行政案件）')
        </where>
    </select>

    <select id="八类恶性犯罪警情统计" resultType="java.util.Map">
        (with all_types as (select '杀人' as type
                            UNION ALL
                            select '强奸' as type
                            UNION ALL
                            select '故意伤害' as type
                            UNION ALL
                            select '抢劫' as type
                            UNION ALL
                            select '纵火' as type
                            UNION ALL
                            select '爆炸' as type
                            UNION ALL
                            select '投放危险物质' as type
                            UNION ALL
                            select '贩卖毒品' as type)
         select at.type as type, count(m.jqlx) as count
         from all_types at
                  left join dwd_gxgx_gx_zhzx_mrjq m on at.type = m.jqlx
         group by at.type)
    </select>
</mapper>
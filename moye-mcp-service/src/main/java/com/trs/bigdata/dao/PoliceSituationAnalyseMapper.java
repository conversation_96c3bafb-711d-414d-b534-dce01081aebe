package com.trs.bigdata.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 警情态势分析Mapper
 */
@Mapper
@DS("mrjq")
public interface PoliceSituationAnalyseMapper {


    /**
     * 全区违法犯罪警情总数
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 全区违法犯罪警情总数(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 全区盗窃类警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 全区盗窃类警情数量(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 全区盗窃类警情高发时段
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 高发时段
     */
    Integer 全区盗窃类警情高发时段(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 全区诈骗类警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 全区诈骗类警情数量(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 全区非侵财类警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 全区非侵财类警情数量(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 全区街面六类警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 全区街面六类侵财警情数量(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 全区殴打他人警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 全区殴打他人警情数量(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 全区家暴警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 全区家暴警情数量(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 龙湖时代天街
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 龙湖时代天街(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 富士康青年公寓
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 警情总数
     */
    Integer 富士康青年公寓(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 全区家暴警情部门统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 全区家暴警情部门统计
     */
    List<Map<String, String>> 家暴类警情部门统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 盗窃类警情部门统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 盗窃类警情部门统计
     */
    List<Map<String, String>> 盗窃类警情部门统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 全区街面六类侵财警情高发时间段
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 全区街面六类侵财警情高发时间段
     */
    Integer 全区街面六类侵财警情高发时间段(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 全区街面六类侵财警情部门统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 全区街面六类侵财警情部门统计
     */
    List<Map<String, String>> 全区街面六类侵财警情部门统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 殴打他人警情部门统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 殴打他人警情部门统计
     */
    List<Map<String, String>> 殴打他人警情部门统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 全区入室盗窃类警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 全区入室盗窃类警情数量
     */
    Integer 全区入室盗窃类警情数量(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 入室盗窃类警情部门统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 入室盗窃类警情部门统计
     */
    List<Map<String, String>> 入室盗窃类警情部门统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 全区电信诈骗类警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 全区电信诈骗类警情数量
     */
    Integer 全区电信诈骗类警情数量(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 电信诈骗类警情部门统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 电信诈骗类警情部门统计
     */
    List<Map<String, String>> 电信诈骗类警情部门统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 全区八类恶性犯罪警情数量
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 八类恶性犯罪警情数量
     */
    Integer 全区八类恶性犯罪警情数量(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 八类恶性犯罪警情统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 八类恶性犯罪警情统计
     */
    List<Map<String, Object>> 八类恶性犯罪警情统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 盗窃类警情分类统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 盗窃类警情分类统计
     */
    List<Map<String, Object>> 盗窃类警情分类统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 街面六类侵财警情分类统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 街面六类侵财警情分类统计
     */
    List<Map<String,Object>> 街面六类侵财警情分类统计(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);
}
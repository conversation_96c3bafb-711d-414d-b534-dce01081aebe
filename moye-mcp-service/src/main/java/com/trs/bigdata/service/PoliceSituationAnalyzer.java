package com.trs.bigdata.service;

import com.trs.bigdata.dao.PoliceSituationAnalyseMapper;
import com.trs.bigdata.pojo.TraceInfo;
import com.trs.moye.base.common.utils.JsonUtils;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.modelcontextprotocol.spec.McpSchema.LoggingLevel;
import io.modelcontextprotocol.spec.McpSchema.LoggingMessageNotification;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

/**
 * 警情分析工具
 */
@Service
@Slf4j
public class PoliceSituationAnalyzer {

    @Resource
    private PoliceSituationAnalyseMapper policeSituationAnalyseMapper;

    @Resource
    private Executor taskExecutor;

    /**
     * 推送日志
     *
     * @param exchange  McpSyncServerExchange
     * @param traceInfo TraceInfo
     */
    public static void sendLog(McpSyncServerExchange exchange, TraceInfo traceInfo) {
        exchange.loggingNotification(
            LoggingMessageNotification.builder().level(LoggingLevel.EMERGENCY).logger("custom-logger")
                .data(JsonUtils.toJsonString(traceInfo)).build());
    }

    /**
     * 警情分析工具
     *
     * @param callId    用户请求id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param exchange  用户会话
     * @return 分析结果
     */
    @Tool(name = "警情分析工具", description = "分析指定时间段内的警情数据，生成警情分析报告")
    public String analyze(
        @ToolParam(description = "用户请求id") String callId,
        @ToolParam(description = "开始时间，格式为 yyyy-MM-dd HH:mm:ss", required = false) LocalDateTime startTime,
        @ToolParam(description = "结束时间，格式为 yyyy-MM-dd HH:mm:ss", required = false) LocalDateTime endTime,
        McpSyncServerExchange exchange) {
        log.info("开始分析警情, callId: {}, startTime: {}, endTime: {}", callId, startTime, endTime);
        sendLog(exchange, new TraceInfo(callId, "开始分析警情", "", false));
        String startTimeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Duration duration = Duration.between(startTime, endTime);
        StringBuilder report = new StringBuilder("\n报告分析开始：\n\n");
        report.append("警情分析报告（高新分局）\n");
        report.append(情况综述(startTime, endTime, duration, startTimeStr, endTimeStr, callId, exchange));
        report.append(重点警情及发案(startTime, endTime, duration, startTimeStr, endTimeStr, callId, exchange));
        report.append("\n报告分析完毕。\n");
        log.info("警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "警情分析完毕", "", true));
        return report.toString();
    }

    private String 情况综述(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析情况综述, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析情况综述", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 全区违法犯罪警情总数 = policeSituationAnalyseMapper.全区违法犯罪警情总数(startTime, endTime);
        final Integer 上期全区违法犯罪警情总数 = policeSituationAnalyseMapper.全区违法犯罪警情总数(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append("一、情况综述：\n")
            .append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区接报违法犯罪警情").append(全区违法犯罪警情总数).append("件,")
            .append("环比上期").append(上期全区违法犯罪警情总数).append("件")
            .append(全区违法犯罪警情总数 > 上期全区违法犯罪警情总数 ? "上升" : "下降")
            .append(String.format("%.2f",
                Math.abs(全区违法犯罪警情总数 - 上期全区违法犯罪警情总数) * 100.0d / 上期全区违法犯罪警情总数))
            .append("%。\n");
        log.info("情况综述分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "情况综述分析完毕", "", false));
        return report.toString();
    }

    private String 重点警情及发案(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析重点警情及发案类型, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析重点警情及发案类型", "", false));
        CompletableFuture<String> f1 = CompletableFuture.supplyAsync(
            () -> 盗窃类警情分析(startTime, endTime, duration, startTimeStr, endTimeStr, callId, exchange),
            taskExecutor);
        CompletableFuture<String> f2 = CompletableFuture.supplyAsync(
            () -> 街面六类侵财警情分析(startTime, endTime, duration, startTimeStr, endTimeStr, callId,
                exchange), taskExecutor);
        CompletableFuture<String> f3 = CompletableFuture.supplyAsync(
            () -> 八类恶性案件警情统计(startTime, endTime, duration, startTimeStr, endTimeStr, callId,
                exchange), taskExecutor);
        CompletableFuture<String> f4 = CompletableFuture.supplyAsync(
            () -> 殴打他人警情分析(startTime, endTime, duration, startTimeStr, endTimeStr, callId, exchange),
            taskExecutor);
        CompletableFuture<String> f5 = CompletableFuture.supplyAsync(
            () -> 家暴类警情分析(startTime, endTime, duration, startTimeStr, endTimeStr, callId, exchange),
            taskExecutor);
        CompletableFuture<String> f6 = CompletableFuture.supplyAsync(
            () -> 入室盗窃类警情分析(startTime, endTime, duration, startTimeStr, endTimeStr, callId,
                exchange), taskExecutor);
        CompletableFuture<String> f7 = CompletableFuture.supplyAsync(
            () -> 电信诈骗类警情分析(startTime, endTime, duration, startTimeStr, endTimeStr, callId,
                exchange), taskExecutor);
        CompletableFuture<String> f8 = CompletableFuture.supplyAsync(
            () -> 热点区域警情通报(startTime, endTime, callId, exchange), taskExecutor);
        String report = "二、重点警情及发案类型分析：\n" + List.of(f1, f2, f3, f4, f5, f6, f7, f8)
            .parallelStream()
            .map(CompletableFuture::join)
            .collect(Collectors.joining());
        log.info("重点警情及发案类型分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "重点警情及发案类型分析完毕", "", false));
        return report;
    }

    private String 盗窃类警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr,
        String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析盗窃类警情, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析盗窃类警情", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 全区盗窃类警情数量 = policeSituationAnalyseMapper.全区盗窃类警情数量(startTime, endTime);
        final Integer 上期全区盗窃类警情数量 = policeSituationAnalyseMapper.全区盗窃类警情数量(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报盗窃类警情").append(全区盗窃类警情数量).append("件");
        if (全区盗窃类警情数量 > 0) {
            report.append("，");
            report.append("环比上期").append(上期全区盗窃类警情数量).append("件")
                .append(全区盗窃类警情数量 > 上期全区盗窃类警情数量 ? "上升" : "下降")
                .append(String.format("%.2f",
                    Math.abs(全区盗窃类警情数量 - 上期全区盗窃类警情数量) * 100.0d / 上期全区盗窃类警情数量))
                .append("%。");
            final Integer 全区盗窃类警情高发时间段 = policeSituationAnalyseMapper.全区盗窃类警情高发时段(startTime,
                endTime);
            if (全区盗窃类警情高发时间段 != null && 全区盗窃类警情高发时间段 >= 0) {
                report.append("盗窃类警情高发时间段为").append(全区盗窃类警情高发时间段).append("时。\n");
            }
            List<Map<String, String>> 盗窃类警情部门统计 = policeSituationAnalyseMapper.盗窃类警情部门统计(startTime,
                endTime);
            if (盗窃类警情部门统计 != null && !盗窃类警情部门统计.isEmpty()) {
                String deptStats = 盗窃类警情部门统计.stream()
                    .map(row -> String.format("%s%s件", row.get("jzgx"), row.get("count")))
                    .collect(Collectors.joining("，"));
                report.append("各部门接报情况为：").append(deptStats).append("。\n");
            }
            List<Map<String, Object>> 盗窃类警情分类统计 = policeSituationAnalyseMapper.盗窃类警情分类统计(startTime,
                endTime);
            if (盗窃类警情分类统计 != null && !盗窃类警情分类统计.isEmpty()) {
                String categoryStats = 盗窃类警情分类统计.stream()
                    .map(row -> String.format("%s类警情%s件", row.get("type"), row.get("count")))
                    .collect(Collectors.joining("，"));
                report.append("各细类警情数量为：").append(categoryStats).append("。\n");
            }
        } else {
            report.append("。\n");
        }
        log.info("盗窃类警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "盗窃类警情分析完毕", "", false));
        return report.toString();
    }

    private String 街面六类侵财警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析街面六类侵财警情, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析街面六类侵财警情", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 全区街面六类侵财警情数量 = policeSituationAnalyseMapper.全区街面六类侵财警情数量(startTime,
            endTime);
        final Integer 上期全区街面六类侵财警情数量 = policeSituationAnalyseMapper.全区街面六类侵财警情数量(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报街面六类侵财警情").append(全区街面六类侵财警情数量).append("件");
        if (全区街面六类侵财警情数量 > 0) {
            report.append("，");
            report.append("环比上期").append(上期全区街面六类侵财警情数量).append("件")
                .append(全区街面六类侵财警情数量 > 上期全区街面六类侵财警情数量 ? "上升" : "下降")
                .append(String.format("%.2f", Math.abs(全区街面六类侵财警情数量 - 上期全区街面六类侵财警情数量) * 100.0d
                    / 上期全区街面六类侵财警情数量))
                .append("%。");
            final Integer 全区街面六类侵财警情高发时间段 = policeSituationAnalyseMapper.全区街面六类侵财警情高发时间段(
                startTime, endTime);
            if (全区街面六类侵财警情高发时间段 != null && 全区街面六类侵财警情高发时间段 >= 0) {
                report.append("街面六类警情高发时间段为").append(全区街面六类侵财警情高发时间段).append("时。\n");
            }
            List<Map<String, String>> 全区街面六类侵财警情部门统计 = policeSituationAnalyseMapper.全区街面六类侵财警情部门统计(
                startTime,
                endTime);
            if (全区街面六类侵财警情部门统计 != null && !全区街面六类侵财警情部门统计.isEmpty()) {
                String deptStats = 全区街面六类侵财警情部门统计.stream()
                    .map(row -> String.format("%s%s件", row.get("jzgx"), row.get("count")))
                    .collect(Collectors.joining("，"));
                report.append("其中各部门接报情况为：").append(deptStats).append("。\n");
            }
            List<Map<String, Object>> 街面六类侵财警情分类统计 = policeSituationAnalyseMapper.街面六类侵财警情分类统计(
                startTime, endTime);
            if (街面六类侵财警情分类统计 != null && !街面六类侵财警情分类统计.isEmpty()) {
                String categoryStats = 街面六类侵财警情分类统计.stream()
                    .map(row -> String.format("%s类警情%s件", row.get("type"), row.get("count")))
                    .collect(Collectors.joining("，"));
                report.append("各细类警情数量为：").append(categoryStats).append("。\n");
            }
        } else {
            report.append("。\n");
        }
        log.info("街面六类侵财警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "街面六类侵财警情分析完毕", "", false));
        return report.toString();
    }

    private String 殴打他人警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析殴打他人警情, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析殴打他人警情", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 全区殴打他人警情数量 = policeSituationAnalyseMapper.全区殴打他人警情数量(startTime, endTime);
        final Integer 上期全区殴打他人警情数量 = policeSituationAnalyseMapper.全区殴打他人警情数量(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报殴打他人警情").append(全区殴打他人警情数量).append("件");
        if (全区殴打他人警情数量 > 0) {
            report.append("，");
            report.append("环比上期").append(上期全区殴打他人警情数量).append("件")
                .append(全区殴打他人警情数量 > 上期全区殴打他人警情数量 ? "上升" : "下降")
                .append(String.format("%.2f",
                    Math.abs(全区殴打他人警情数量 - 上期全区殴打他人警情数量) * 100.0d / 上期全区殴打他人警情数量))
                .append("%。");
            List<Map<String, String>> 殴打他人警情部门统计 = policeSituationAnalyseMapper.殴打他人警情部门统计(
                startTime,
                endTime);
            if (殴打他人警情部门统计 != null && !殴打他人警情部门统计.isEmpty()) {
                String deptStats = 殴打他人警情部门统计.stream()
                    .map(row -> String.format("%s%s件", row.get("jzgx"), row.get("count")))
                    .collect(Collectors.joining("，"));
                report.append("其中各部门接报情况为：").append(deptStats).append("。\n");
            }
        } else {
            report.append("。\n");
        }
        log.info("殴打他人警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "殴打他人警情分析完毕", "", false));
        return report.toString();
    }

    private String 家暴类警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析家暴类警情, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析家暴类警情", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 全区家暴类警情数量 = policeSituationAnalyseMapper.全区家暴警情数量(startTime, endTime);
        final Integer 上期全区家暴警情数量 = policeSituationAnalyseMapper.全区家暴警情数量(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报家暴类警情").append(全区家暴类警情数量).append("件");
        if (全区家暴类警情数量 > 0) {
            report.append("，");
            report.append("环比上期").append(上期全区家暴警情数量).append("件")
                .append(全区家暴类警情数量 > 上期全区家暴警情数量 ? "上升" : "下降")
                .append(String.format("%.2f",
                    Math.abs(全区家暴类警情数量 - 上期全区家暴警情数量) * 100.0d / 上期全区家暴警情数量))
                .append("%。");
            List<Map<String, String>> 家暴类警情部门统计 = policeSituationAnalyseMapper.家暴类警情部门统计(startTime,
                endTime);
            if (家暴类警情部门统计 != null && !家暴类警情部门统计.isEmpty()) {
                String deptStats = 家暴类警情部门统计.stream()
                    .map(row -> String.format("%s%s件", row.get("jzgx"), row.get("count")))
                    .collect(Collectors.joining("，"));
                report.append("其中各部门接报情况为：").append(deptStats).append("。\n");
            }
        } else {
            report.append("。\n");
        }
        log.info("家暴类警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "家暴类警情分析完毕", "", false));
        return report.toString();
    }

    private String 入室盗窃类警情分析(LocalDateTime beginTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析入室盗窃类警情, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析入室盗窃", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 全区入室盗窃类警情数量 = policeSituationAnalyseMapper.全区入室盗窃类警情数量(beginTime, endTime);
        final Integer 上期全区入室盗窃类警情数量 = policeSituationAnalyseMapper.全区入室盗窃类警情数量(
            beginTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ");
        report.append("全区共接报入室盗窃类警情").append(全区入室盗窃类警情数量).append("件");
        if (全区入室盗窃类警情数量 > 0) {
            report.append("，");
            report.append("环比上期").append(上期全区入室盗窃类警情数量).append("件")
                .append(全区入室盗窃类警情数量 > 上期全区入室盗窃类警情数量 ? "上升" : "下降")
                .append(String.format("%.2f",
                    Math.abs(全区入室盗窃类警情数量 - 上期全区入室盗窃类警情数量) * 100.0d
                        / 上期全区入室盗窃类警情数量))
                .append("%。");
            List<Map<String, String>> 入室盗窃类警情部门统计 = policeSituationAnalyseMapper.入室盗窃类警情部门统计(
                beginTime,
                endTime);
            if (入室盗窃类警情部门统计 != null && !入室盗窃类警情部门统计.isEmpty()) {
                String deptStats = 入室盗窃类警情部门统计.stream()
                    .map(row -> String.format("%s%s件", row.get("jzgx"), row.get("count")))
                    .collect(Collectors.joining("，"));
                report.append("其中各部门接报情况为：").append(deptStats).append("。\n");
            }
        } else {
            report.append("。\n");
        }
        log.info("入室盗窃类警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "入室盗窃类警情分析完毕", "", false));
        return report.toString();
    }

    private String 电信诈骗类警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析电信诈骗类警情, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析电信诈骗类警情", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 全区电信诈骗类警情数量 = policeSituationAnalyseMapper.全区电信诈骗类警情数量(startTime, endTime);
        final Integer 上期全区电信诈骗类警情数量 = policeSituationAnalyseMapper.全区电信诈骗类警情数量(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报电信诈骗类警情").append(全区电信诈骗类警情数量).append("件");
        if (全区电信诈骗类警情数量 > 0) {
            report.append("，");
            report.append("环比上期").append(上期全区电信诈骗类警情数量).append("件")
                .append(全区电信诈骗类警情数量 > 上期全区电信诈骗类警情数量 ? "上升" : "下降")
                .append(String.format("%.2f",
                    Math.abs(全区电信诈骗类警情数量 - 上期全区电信诈骗类警情数量) * 100.0d
                        / 上期全区电信诈骗类警情数量))
                .append("%。");
            List<Map<String, String>> 电信诈骗类警情部门统计 = policeSituationAnalyseMapper.电信诈骗类警情部门统计(
                startTime,
                endTime);
            if (电信诈骗类警情部门统计 != null && !电信诈骗类警情部门统计.isEmpty()) {
                String deptStats = 电信诈骗类警情部门统计.stream()
                    .map(row -> String.format("%s%s件", row.get("jzgx"), row.get("count")))
                    .collect(Collectors.joining("，"));
                report.append("其中各部门接报情况为：").append(deptStats).append("。\n");
            }
        } else {
            report.append("。\n");
        }
        log.info("电信诈骗类警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "电信诈骗类警情分析完毕", "", false));
        return report.toString();
    }

    private String 八类恶性案件警情统计(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr, String callId, McpSyncServerExchange exchange) {
        log.info("开始分析八类恶性案件警情, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析八类恶性案件警情", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 全区八类恶性犯罪警情数量 = policeSituationAnalyseMapper.全区八类恶性犯罪警情数量(startTime,
            endTime);
        final Integer 上期全区八类恶性犯罪警情数量 = policeSituationAnalyseMapper.全区八类恶性犯罪警情数量(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报八类恶性犯罪警情").append(全区八类恶性犯罪警情数量).append("件");
        if (全区八类恶性犯罪警情数量 > 0) {
            report.append("，");
            report.append("环比上期").append(上期全区八类恶性犯罪警情数量).append("件")
                .append(全区八类恶性犯罪警情数量 > 上期全区八类恶性犯罪警情数量 ? "上升" : "下降")
                .append(String.format("%.2f",
                    Math.abs(全区八类恶性犯罪警情数量 - 上期全区八类恶性犯罪警情数量) * 100.0d
                        / 上期全区八类恶性犯罪警情数量))
                .append("%。");
            final List<Map<String, Object>> 八类恶性犯罪警情统计 = policeSituationAnalyseMapper.八类恶性犯罪警情统计(
                startTime, endTime);
            八类恶性犯罪警情统计.forEach(row -> {
                Object jqlx = row.get("type");
                Object count = row.get("count");
                if (jqlx != null && count != null) {
                    report.append("%s类警情%s件，".formatted(jqlx.toString(), count.toString()));
                }
            });
            report.append("\n");
        }
        log.info("八类恶性案件警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "八类恶性案件警情分析完毕", "", false));
        return report.toString();
    }

    private String 热点区域警情通报(LocalDateTime startTime, LocalDateTime endTime, String callId,
        McpSyncServerExchange exchange) {
        log.info("开始分析热点区域警情, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "开始分析热点区域警情", "", false));
        StringBuilder report = new StringBuilder();
        final Integer 龙湖时代天街 = policeSituationAnalyseMapper.龙湖时代天街(startTime, endTime);
        final Integer 富士康青年公寓 = policeSituationAnalyseMapper.富士康青年公寓(startTime, endTime);
        report.append("三、热点区域警情通报：\n")
            .append("龙湖时代天街接报警情").append(龙湖时代天街).append("件，")
            .append("富士康青年公寓接报警情").append(富士康青年公寓).append("件。\n");
        log.info("热点区域警情分析完毕, callId: {}", callId);
        sendLog(exchange, new TraceInfo(callId, "热点区域警情分析完毕", "", false));
        return report.toString();
    }
}

package com.trs.moye.base.monitor.entity.spark;

import com.trs.moye.base.common.enums.Mode;
import com.trs.moye.base.monitor.enums.ClusterState;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 集群资源状态
 *
 * <AUTHOR>
 * @since 2025/09/03
 */
@Data
public class SparkResourceStatus {

    /**
     * CPU核心总数
     */
    private int totalCpuCores;

    /**
     * 已使用CPU核心数
     */
    private int usedCpuCores;

    /**
     * 内存总量(包含单位，如 GB, MB, KB)
     */
    private String totalMemory;

    /**
     * 已使用内存量(包含单位，如 GB, MB, KB)
     */
    private String usedMemory;

    /**
     * 检测时间/采集时间
     */
    private LocalDateTime queryTime;

    /**
     * 集群状态
     */
    private ClusterState state;

    /**
     * 模式
     */
    private Mode mode;

    /**
     * 活跃worker数（standalone模式为工作节点数，yarn模式为节点数）
     */
    private int activeWorkers;
}
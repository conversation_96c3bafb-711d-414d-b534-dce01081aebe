package com.trs.moye.base.common.response;

import com.trs.moye.base.common.constants.ErrorMessages;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 信息包装返回类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/10/19 20:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResponseMessage {

    private int code;

    private boolean success;

    private String message;

    private Object data;

    private String detail;


    /**
     * 构造函数
     *
     * @param code    状态码
     * @param success 是否成功
     * @param message 消息
     * @param data    数据
     */
    public ResponseMessage(int code, boolean success, String message, Object data) {
        this.code = code;
        this.success = success;
        this.message = message;
        this.data = data;
    }

    /**
     * 错误
     *
     * @param msg 返回消息
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     */
    public static ResponseMessage error(String msg) {
        return new ResponseMessage(500, false, msg, null);
    }

    /**
     * 构造错误信息的应答消息
     *
     * @param msg     错误消息
     * @param details 错误详情
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     */
    public static ResponseMessage error(String msg, String details) {
        return new ResponseMessage(500, false, msg, null, details);
    }

    /**
     * 构造错误信息的应答消息
     *
     * @param msg  错误消息
     * @param data 错误数据
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     */
    public static ResponseMessage error(String msg, Object data) {
        return new ResponseMessage(500, false, msg, data);
    }

    /**
     * 构造错误信息的应答消息
     *
     * @param status 错误状态码
     * @param msg    错误消息
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     * <AUTHOR>
     * @since 2020/9/9 10:57 下午
     */
    public static ResponseMessage errorWithStatus(int status, String msg) {
        return new ResponseMessage(status, false, msg, null);
    }

    /**
     * 构造错误信息的应答消息
     *
     * @param status 错误状态码
     * @param msg    错误消息
     * @param data   错误数据
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     */
    public static ResponseMessage errorWithStatus(int status, String msg, Object data) {
        return new ResponseMessage(status, false, msg, data);
    }

    /**
     * 成功
     *
     * @param o 返回数据
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     */
    public static ResponseMessage ok(Object o) {
        return new ResponseMessage(200, true, null, o);
    }

    /**
     * 成功
     *
     * @param status 状态码
     * @param o      返回数据
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     */
    public static ResponseMessage okWithStatus(int status, Object o) {
        return new ResponseMessage(status, true, null, o);
    }

    /**
     * 未登陆
     *
     * @param o 返回数据
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     */
    public static ResponseMessage unauthorized(Object o) {
        return new ResponseMessage(401, false, ErrorMessages.UNAUTHORIZED_ROLE, o);
    }

    /**
     * 未找到
     *
     * @param o 返回数据
     * @return com.trs.ai.my.common.ResponseMessage 返回给请求方的响应消息
     */
    public static ResponseMessage notFound(Object o) {
        return new ResponseMessage(404, false, ErrorMessages.NOT_FOUND, o);
    }
}

package com.trs.moye.base.data.model.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.storage.IncrementInfo;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * DataModelExecuteConfig数据访问层
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/09/26 17:15
 */
@Mapper
public interface DataModelExecuteConfigMapper extends BaseMapper<DataModelExecuteConfig> {

    /**
     * 条件删除：通过dataModelId删除
     *
     * @param dataModelId dataModelId
     * @return int
     */
    int deleteByDataModelId(@Param("dataModelId") Integer dataModelId);


    /**
     * 主键查询
     *
     * @param id id
     * @return DataModelExecuteConfig
     */
    DataModelExecuteConfig selectByPrimaryKey(@Param("id") Integer id);

    /**
     * id集合查询
     *
     * @param idCollection id集合
     * @return 数据列表
     */
    List<DataModelExecuteConfig> selectByIdCollection(@Param("idCollection") Collection<Integer> idCollection);

    /**
     * 单条查询：通过dataModelId查询
     *
     * @param dataModelId dataModelId
     * @return DataModelExecuteConfig
     */
    DataModelExecuteConfig selectByDataModelId(@Param("dataModelId") Integer dataModelId);

    /**
     * 集合查询：通过dataModelIdCollection查询
     *
     * @param dataModelIdCollection dataModelId集合
     * @return 数据列表
     */
    List<DataModelExecuteConfig> listByDataModelIdCollection(
        @Param("dataModelIdCollection") Collection<Integer> dataModelIdCollection);

    /**
     * 根据dataModelId更新增量信息
     *
     * @param incrementValue 增量信息
     * @param dataModelId    数据建模Id
     */
    void updateIncrementValueByDataModelId(@Param("incrementValue") Object incrementValue,
        @Param("dataModelId") Integer dataModelId);

    /**
     * 根据dataModelId更新执行参数
     *
     * @param dataModelId   建模id
     * @param executeParams 执行参数
     */
    void updateExecuteParamsByDataModelId(@Param("dataModelId") Integer dataModelId,
        @Param("executeParams") String executeParams);

    /**
     * 根据dataModelId更新增量信息
     *
     * @param executeConfigId 执行配置id
     * @param incrementInfo 增量信息
     */
    void updateIncrementInfoById(@Param("executeConfigId") Integer executeConfigId, @Param("incrementInfo") IncrementInfo incrementInfo);
}
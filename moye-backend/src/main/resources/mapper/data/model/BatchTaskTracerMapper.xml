<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.data.model.dao.BatchTaskTracerMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.data.model.entity.BatchTaskTracer">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="node" property="node"/>
        <result column="execute_id" jdbcType="VARCHAR" property="executeId"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="process_time" jdbcType="BIGINT" property="processTime"/>
        <result column="is_error" jdbcType="TINYINT" property="isError"/>
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
        <result column="data_count" jdbcType="BIGINT" property="dataCount"/>
        <result column="output_table_name" jdbcType="VARCHAR" property="outputTableName"/>
        <result column="sample_data" jdbcType="VARCHAR" property="sampleData" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="fields" jdbcType="VARCHAR" property="fields" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="pre_process_data_count" jdbcType="BIGINT" property="preProcessDataCount"/>
        <result column="arranged_name" jdbcType="VARCHAR" property="arrangedName"/>
        <result column="input_table_name" jdbcType="VARCHAR" property="inputTableName"/>
        <result column="conditions" jdbcType="VARCHAR" property="conditions"/>
        <result column="conditions_json" jdbcType="VARCHAR" property="conditionsJson"/>
        <result column="condition_result" jdbcType="TINYINT" property="conditionResult"/>
    </resultMap>


    <select id="listByExecuteIdOrderByStartTimeAsc" resultMap="BaseResultMap">
        select *
        from batch_task_tracer
        <where>
            and execute_id = #{executeId,jdbcType=VARCHAR}
        </where>
        order by start_time asc
    </select>

    <select id="listLatestErrorByExecuteIds" resultMap="BaseResultMap">
        SELECT *
        FROM batch_task_tracer
        WHERE id IN (
            SELECT id
            FROM (
                SELECT id,
                       row_number() OVER (PARTITION BY execute_id ORDER BY start_time DESC) as rn
                FROM batch_task_tracer
                WHERE is_error = 1
                AND execute_id IN
                <foreach collection="executeIds" item="executeId" open="(" separator="," close=")">
                    #{executeId}
                </foreach>
            ) t
            WHERE rn = 1
        )
    </select>

    <!-- BatchTaskTracerMapper.xml -->
    <select id="getProcessCount" resultType="java.lang.Long">
        SELECT COALESCE(SUM(data_count), 0)
        FROM batch_task_tracer
        WHERE node = 'input'
        AND execute_id IN (
        SELECT execute_id
        FROM batch_task_record
        WHERE task_id = #{dataModelId}
        <if test="startTime != null">
            AND start_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[ AND start_time <= #{endTime} ]]>
        </if>
        )
    </select>

    <select id="getStorageCount" resultType="java.lang.Long">
        SELECT COALESCE(SUM(data_count), 0)
        FROM batch_task_tracer
        WHERE node = 'save'
        AND execute_id IN (
        SELECT execute_id
        FROM batch_task_record
        WHERE task_id = #{dataModelId}
        <if test="startTime != null">
            AND start_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            <![CDATA[ AND start_time <= #{endTime} ]]>
        </if>
        )
    </select>

</mapper>
package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.ai.moye.batchengine.entity.ExecuteResultMap;
import com.trs.ai.moye.batchengine.entity.ExecuteResultRequest;
import com.trs.ai.moye.batchengine.service.BatchEngineService;
import com.trs.ai.moye.common.entity.UsageInfoResponse.UsingObjects;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.dao.batch.BatchOperatorMapper;
import com.trs.ai.moye.data.model.dto.arrangement.batch.BatchOperatorDTO;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import com.trs.ai.moye.data.model.service.BatchArrangementService;
import com.trs.ai.moye.permission.service.CurrentUserService;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import com.trs.moye.base.data.model.entity.DataModel;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 批处理算子编排service实现
 *
 * <AUTHOR>
 * @since 2024/10/21 15:15
 */
@Service
@Slf4j
public class BatchArrangementServiceImpl implements BatchArrangementService {

    @Resource
    private BatchOperatorMapper batchOperatorMapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private BatchArrangementMapper batchArrangementMapper;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private BatchEngineService batchEngineService;

    @Resource
    private AbilityMapper abilityMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatchArrange(Integer dataModelId) {
        batchArrangementMapper.deleteByDataModelId(dataModelId);
        batchOperatorMapper.deleteByDataModelId(dataModelId);
    }

    @Override
    public Boolean execute(Integer dataModelId, BatchProcessSparkConfig config) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        BatchEngineTaskParam task = new BatchEngineTaskParam(dataModelId.toString(), dataModel.getLayer(),
            dataModel.getZhName());
        task.setSparkConfig(config);
        task.setUserId(currentUserService.getUserId());
        task.setBeginTime(config.getBeginTime());
        task.setEndTime(config.getEndTime());
        return batchEngineService.executeJava(task);
    }

    /**
     * 测试dag模式任务
     *
     * @param dataModelId 数据建模ID
     * @param config      前端请求参数
     * @param operators   算子列表
     * @return {@link Boolean}
     */
    @Override
    public List<BatchTaskTracer> test(Integer dataModelId, BatchProcessSparkConfig config,
        List<BatchOperatorDTO> operators) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        BatchEngineTaskParam task = new BatchEngineTaskParam(dataModelId.toString(), dataModel.getLayer(),
            dataModel.getZhName());
        task.setSparkConfig(config);
        task.setUserId(currentUserService.getUserId());

        // 处理算子列表
        List<BatchOperator> batchOperators = operators.stream().map(operator -> operator.toBatchOperator(null, dataModelId)).toList();
        BatchOperator.assignParentDisplayIds(batchOperators);
        // 添加ability实体
        batchOperators = setAbilities(batchOperators);

        task.setOperators(batchOperators);
        return batchEngineService.testJava(task);
    }

    @NotNull
    private List<BatchOperator> setAbilities(List<BatchOperator> batchOperators) {
        Set<Integer> abilityIds = batchOperators.stream().map(BatchOperator::getAbilityId)
            .filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(abilityIds)) {
            return batchOperators;
        }

        Map<Integer, Ability> abilityMap = abilityMapper.selectBatchIds(abilityIds).stream()
            .collect(Collectors.toMap(Ability::getId, ability -> ability));

        batchOperators = batchOperators.stream().peek(operator -> {
            if (operator.getAbilityId() != null) {
                Ability ability = abilityMap.get(operator.getAbilityId());
                operator.setAbility(ability);
            }
        }).toList();
        return batchOperators;
    }

    @Override
    public UsingObjects getStorageUsage(Integer storageId) {
        UsingObjects usingObjects = new UsingObjects(ModuleEnum.DATA_MODELING);
        usingObjects.setObjects(batchOperatorMapper.selectStoragePointUsingBatchModel(storageId));
        return usingObjects;
    }

    @Override
    public List<ExecuteResultMap> executeResult(ExecuteResultRequest executeResultRequest) {
        return batchEngineService.executeResult(executeResultRequest);
    }

}

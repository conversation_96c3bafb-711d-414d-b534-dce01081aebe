package com.trs.ai.moye.data.model.controller;

import com.trs.ai.moye.batchengine.service.BatchEngineService;
import com.trs.ai.moye.common.utils.MinioUtil;
import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.data.model.entity.BatchTaskRecord;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import com.trs.ai.moye.data.model.enums.DwdRealtimeMonitorType;
import com.trs.ai.moye.data.model.request.BatchProcessDataListRequest;
import com.trs.ai.moye.data.model.request.BatchTaskLogDetailRequest;
import com.trs.ai.moye.data.model.request.BatchTaskLogRequest;
import com.trs.ai.moye.data.model.request.DwdStartRequest;
import com.trs.ai.moye.data.model.request.ProcessRetryRequest;
import com.trs.ai.moye.data.model.request.StreamProcessDataListRequest;
import com.trs.ai.moye.data.model.response.BatchTaskLogResponse;
import com.trs.ai.moye.data.model.response.BatchTaskLogResponse.BatchTaskLogResponseItem;
import com.trs.ai.moye.data.model.response.BatchTaskRecordResponse;
import com.trs.ai.moye.data.model.response.BatchTaskTracerResponse;
import com.trs.ai.moye.data.model.response.DataProcessRecordResponse;
import com.trs.ai.moye.data.model.response.DwdExecuteScheduleResponse;
import com.trs.ai.moye.data.model.response.DwdRealtimeConsumeInfoResponse;
import com.trs.ai.moye.data.model.response.DwdRealtimeMonitorResponse;
import com.trs.ai.moye.data.model.response.ProcessFlowResponse;
import com.trs.ai.moye.data.model.response.RerunMsgCount;
import com.trs.ai.moye.data.model.service.DwdModelRealtimeMonitorService;
import com.trs.ai.moye.data.model.service.DwdModelService;
import com.trs.ai.moye.data.model.service.impl.StreamLogService;
import com.trs.ai.moye.minio.starter.properties.MinioProperties;
import com.trs.ai.moye.monitor.response.MonitorTrendResponse;
import com.trs.moye.base.common.annotaion.OperateLogSign;
import com.trs.moye.base.common.constants.ErrorMessages;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.log.operate.OperateType;
import com.trs.moye.base.common.request.BaseRequestParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.monitor.entity.spark.SparkResourceStatus;
import io.minio.MinioClient;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 要素库相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/12 10:37
 **/
@Slf4j
@RestController
@RequestMapping("/data-model/dwd")
@Validated
public class DwdModelController {

    @Resource
    private DwdModelService dwdModelService;
    @Resource
    private BatchEngineService batchEngineService;
    @Resource
    private StorageTaskMapper storageTaskMapper;
    @Resource
    private DwdModelRealtimeMonitorService dwdModelRealtimeMonitorService;
    @Resource
    private MinioClient minioClient;
    @Resource
    private MinioProperties minioProperties;
    @Resource
    private StreamLogService streamLogService;


    /**
     * 启动任务
     *
     * @param id              建模id
     * @param dwdStartRequest 要素库
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, operateType = OperateType.EXECUTE_TASK, apiName = "启动要素库任务")
    @PostMapping("/{id}/start")
    public void startTask(@PathVariable Integer id,
        @RequestBody(required = false) DwdStartRequest dwdStartRequest) {
        dwdModelService.startTask(id, dwdStartRequest);
    }

    /**
     * 停止任务
     *
     * @param id 贴源库id
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, operateType = OperateType.STOP_TASK, apiName = "停止要素库任务")
    @PostMapping("/{id}/stop")
    public void stopTask(@PathVariable Integer id) {
        dwdModelService.stopTask(id);
    }

    /**
     * 暂停任务
     *
     * @param id 贴源库id
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, operateType = OperateType.STOP_TASK, apiName = "暂停要素库任务")
    @PostMapping("/{id}/pause")
    public void pauseTask(@PathVariable Integer id) {
        dwdModelService.pauseTask(id);
    }


    /**
     * 获取处理数据监控列表 <a href="http://**************:3001/project/5419/interface/api/164995">...</a>
     *
     * @param request 前端请求参数
     * @return {@link BatchTaskRecordResponse}
     * <AUTHOR>
     * @since 2024/10/17 14:17
     */
    @PostMapping("/batch/monitor/page-list")
    public PageResponse<BatchTaskRecordResponse> getBatchProcessDataList(
        @RequestBody BatchProcessDataListRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getPageParams())) {
            throw new BizException(ErrorMessages.INVALID_PARAMETER);
        }
        return dwdModelService.getBatchProcessDataList(request);
    }

    /**
     * 停止spark任务 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/166189">...</a>
     *
     * @param executeId 批处理任务执行id
     * <AUTHOR>
     * @since 2024/12/11 15:49
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, operateType = OperateType.STOP_TASK, apiName = "停止spark任务")
    @PostMapping("/batch/monitor/kill/{executeId}")
    public void killTask(@PathVariable String executeId) {
        batchEngineService.killTask(executeId);
    }


    /**
     * 获取流处理数据列表，处理监控  <a href="http://**************:3001/project/5419/interface/api/164215">...</a>
     *
     * @param request 前端请求参数
     * @return {@link BatchTaskRecord }
     * <AUTHOR>
     * @since 2024/10/17 16:44
     */
    @PostMapping("/stream/monitor/page-list")
    public PageResponse<DataProcessRecordResponse> getStreamProcessDataList(
        @RequestBody @Validated StreamProcessDataListRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getPageParams())) {
            throw new BizException(ErrorMessages.INVALID_PARAMETER);
        }
        return streamLogService.getStreamProcessDataList(request);
    }

    /**
     * 流处理----流处理监控链路 <a href="http://yapi.devdemo.trs.net.cn/project/4220/interface/api/125578">...</a>
     *
     * @param dataModelId 数据模型id
     * @param recordId    数据id
     * @return {@link ProcessFlowResponse}
     * <AUTHOR>
     * @since 2024/10/25 17:39
     */
    @GetMapping("/stream/monitor/tracer")
    public List<ProcessFlowResponse> getProcessFlow(@RequestParam Integer dataModelId, @RequestParam String recordId) {
        return streamLogService.getDataProcessTraces(dataModelId, recordId);
    }

    /**
     * 流处理---[处理监控]验证异常算子
     *
     * @param request 前端请求
     * @return {@link ProcessFlowResponse}
     * <AUTHOR>
     * @since 2024/10/25 19:19
     */
    @PostMapping("/monitor/retry")
    public ProcessFlowResponse retry(@RequestBody ProcessRetryRequest request) throws BizException {
        if (request.getAbilityId() == null) {
            throw new BizException(ErrorMessages.INVALID_PARAMETER + ", abilityId 为空");
        }
        return streamLogService.retryProcess(request);
    }

    /**
     * 重跑异常数据
     *
     * @param dataModelId     数据模型ID
     * @param timeRangeParams 时间范围
     */
    @PostMapping("/stream/{dataModelId}/monitor/rerun")
    public void rerun(@PathVariable Integer dataModelId, @RequestBody TimeRangeParams timeRangeParams) {
        streamLogService.rerun(dataModelId, timeRangeParams);
    }

    /**
     * 获取异常数据数量
     *
     * @param dataModelId     数据模型ID
     * @param timeRangeParams 时间范围
     * @return 异常数据数量
     */
    @PostMapping("/stream/{dataModelId}/monitor/rerun/count")
    public RerunMsgCount getRerunMsgCount(@PathVariable Integer dataModelId,
        @RequestBody TimeRangeParams timeRangeParams) {
        return streamLogService.getRerunMsgCount(dataModelId, timeRangeParams);
    }

    /**
     * 批处理----处理监控查看流程  <a href="http://**************:3001/project/5419/interface/api/165255">...</a>
     *
     * @param executeId 执行ID
     * @return {@link BatchTaskTracer}
     * <AUTHOR>
     * @since 2024/10/25 16:21
     */
    @GetMapping("/batch/record-tracer/list")
    public List<BatchTaskTracerResponse> getBatchTaskRecordTracerList(
        @RequestParam @NotBlank(message = "执行id不允许为空") String executeId) {
        return dwdModelService.getBatchTaskRecordTracerList(executeId).stream()
            .map(BatchTaskTracerResponse::fromBatchTaskTracer).toList();
    }


    /**
     * 批处理---处理监控获取日志 <a href="http://**************:3001/project/5419/interface/api/165265">...</a>
     *
     * @param request 前端请求参数
     * @return {@link BatchTaskLogResponse}
     * <AUTHOR>
     * @since 2024/10/29 10:58
     */
    @PostMapping("/batch/monitor/log/list")
    public List<BatchTaskLogResponse> getBatchLogs(@RequestBody @Valid BatchTaskLogRequest request)
        throws BizException {
        return dwdModelService.getBatchLogs(request);
    }

    /**
     * 批处理---下载处理监控日志 <a href="http://**************:3001/project/5419/interface/api/168856">YApi</a>
     *
     * @param request  前端请求参数
     * @param response 响应对象
     */
    @PostMapping("/batch/monitor/log/list/download")
    public void download(@RequestBody @Valid BatchTaskLogRequest request, HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition",
                String.format("attachment; filename=logs_%s.zip", request.getExecuteId()));

            // 获取日志文件路径列表
            List<String> logPaths = dwdModelService.getBatchLogObjects(request);

            // 重命名
            List<BatchTaskLogResponseItem> batchTaskLogResponseItems = logPaths.parallelStream()
                .map(path -> BatchTaskLogResponseItem.fromPath(path, null)).toList();
            List<BatchTaskLogResponse> batchTaskLogResponse = BatchTaskLogResponse.from(request.getTaskId(),
                request.getExecuteId(), batchTaskLogResponseItems);

            // 创建ZIP输出流
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                for (BatchTaskLogResponse taskLogResponse : batchTaskLogResponse) {
                    // 文件夹名
                    String folderName = taskLogResponse.getName();
                    List<BatchTaskLogResponseItem> items = taskLogResponse.getItems();
                    if (CollectionUtils.isEmpty(items)) {
                        continue;
                    }
                    for (BatchTaskLogResponseItem item : items) {
                        try (InputStream inputStream = MinioUtil.getObjectStream(item.getPath(), minioClient,
                            minioProperties.getBucket().getLogs())) {
                            // 添加ZIP条目
                            zipOut.putNextEntry(new ZipEntry(folderName + "/" + item.getName()));
                            IOUtils.copy(inputStream, zipOut);
                            zipOut.closeEntry();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("下载批处理监控日志文件失败", e);
            throw new BizException("下载日志文件失败: " + e.getMessage());
        }
    }


    /**
     * 批处理----处理监控，获取日志详情 日志文件 <a href="http://**************:3001/project/5419/interface/api/165270">...</a>
     *
     * @param request 前端请求
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/10/29 11:06
     */
    @PostMapping("/batch/monitor/log/detail")
    public String getLogFileByLogPath(@RequestBody @Valid BatchTaskLogDetailRequest request) {
        return dwdModelService.getLogFileByLogPath(request.getPath());
    }

    /**
     * 更新数据来源
     *
     * @param dataModelId   数据建模id
     * @param dataSourceIds 数据来源id
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "要素库更新数据来源")
    @PutMapping("/{dataModelId}/data-source")
    public void updateDataSource(@PathVariable("dataModelId") Integer dataModelId,
        @RequestBody List<Integer> dataSourceIds) {
        dwdModelService.updateDataSource(dataModelId, dataSourceIds);
    }


    /**
     * 要素库查询未读的错误调度信息  <a href="http://**************:3001/project/5419/interface/api/167353>...</a>
     *
     * @param dataModelId 数据建模ID
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2024/10/21 16:42
     */
    @GetMapping("/{dataModelId}/schedule/error-count")
    public Integer getErrorCount(@NotNull @PathVariable Integer dataModelId) {
        return storageTaskMapper.countErrorByDataModelId(dataModelId);
    }

    /**
     * 要素库调度错误信息已读 <a href="http://**************:3001/project/5419/interface/api/167359">...</a>
     *
     * @param id 单条数据的主键ID
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/21 17:27
     */
    @PutMapping("/{id}/read/schedule/error-messages")
    public Boolean readErrorMessages(@NotNull @PathVariable String id) {
        return storageTaskMapper.updateReadErrorMessages(id);
    }


    /**
     * 获取实时要素库消费监控列表
     * <a href="http://**************:3001/project/5419/interface/api/167383">【要素库】实时要素库积压-详情列表</a>
     *
     * @param dataModelId id
     * @param request     请求
     * @return {@link PageResponse }<{@link DwdRealtimeMonitorResponse }>
     * <AUTHOR>
     * @since 2025/02/18 17:32:00
     */
    @PostMapping("/{dataModelId}/realtime/monitor/list")
    public PageResponse<DwdRealtimeMonitorResponse> getStreamMonitorList(
        @PathVariable Integer dataModelId,
        @RequestBody @Validated BaseRequestParams request) {
        if (Objects.isNull(request) || Objects.isNull(request.getPageParams())) {
            throw new BizException(ErrorMessages.INVALID_PARAMETER);
        }
        return dwdModelRealtimeMonitorService.getMonitorRecordPageList(dataModelId, request);
    }

    /**
     * 获取 DWD TPS趋势
     * <a href="http://**************:3001/project/5419/interface/api/167377">【要素库】实时要素库积压/TPS-详情趋势</a>
     *
     * @param dataModelId id
     * @param monitorType 类型
     * @param request     请求
     * @return {@link List }<{@link MonitorTrendResponse }>
     * <AUTHOR>
     * @since 2025/02/18 17:33:19
     */
    @PostMapping("/{dataModelId}/realtime/monitor/{monitorType}/trend")
    public List<MonitorTrendResponse> getDwdRealtimeTrend(@PathVariable Integer dataModelId,
        @PathVariable String monitorType,
        @RequestBody @Valid BaseRequestParams request) {
        DwdRealtimeMonitorType typeEnum = DwdRealtimeMonitorType.valueOf(String.valueOf(monitorType).toUpperCase());
        if (DwdRealtimeMonitorType.LAG.equals(typeEnum)) {
            return dwdModelRealtimeMonitorService.getMonitorLagTrend(dataModelId, request);
        } else if (DwdRealtimeMonitorType.TPS.equals(typeEnum)) {
            return dwdModelRealtimeMonitorService.getMonitorTpsTrend(dataModelId, request);
        }
        throw new BizException("监控类型【%s】不提供趋势图", monitorType);
    }

    /**
     * 获取 DWD 实时监控最新记录
     * <a href="http://**************:3001/project/5419/interface/api/167476">【要素库】实时要素库 最新监控记录</a>
     *
     * @param dataModelId id
     * @return {@link DwdRealtimeMonitorResponse}
     */
    @GetMapping("/{dataModelId}/realtime/monitor/lasted")
    public DwdRealtimeMonitorResponse getLastedMonitorRecord(@PathVariable Integer dataModelId) {
        return dwdModelRealtimeMonitorService.getLastedMonitorRecord(dataModelId);
    }

    /**
     * 刷新监控记录
     * <a href="http://**************:3001/project/5419/interface/api/167479">【要素库】实时要素库 刷新监控信息</a>
     *
     * @param dataModelId 数据型id
     * @return {@link DwdRealtimeConsumeInfoResponse }
     */
    @GetMapping("/{dataModelId}/realtime/monitor/refresh")
    public DwdRealtimeConsumeInfoResponse refreshMonitorRecord(@PathVariable Integer dataModelId) {
        return dwdModelRealtimeMonitorService.refreshMonitorRecord(dataModelId);
    }

    /**
     * 获取要素库执行计划信息
     * <a href="http://**************:3001/project/5419/interface/api/167512">【要素库】定时要素库-获取定时执行信息</a>
     *
     * @param dataModelId 数据型id
     * @return {@link DwdExecuteScheduleResponse }
     * <AUTHOR>
     * @since 2025/02/21 18:13:28
     */
    @GetMapping("/{dataModelId}/schedule/execute-info")
    public DwdExecuteScheduleResponse getModelExecuteScheduleInfo(@PathVariable Integer dataModelId) {
        return dwdModelService.getModelExecuteScheduleInfo(dataModelId);
    }

    /**
     * 获取Spark集群资源状态
     *
     * @param dataModelId 数据模型ID
     * @return 集群资源状态
     */
    @GetMapping("/{dataModelId}/cluster/resource/status")
    public SparkResourceStatus getClusterResourceStatus(@PathVariable Integer dataModelId) {
        return batchEngineService.getClusterResourceStatus(dataModelId);
    }
}

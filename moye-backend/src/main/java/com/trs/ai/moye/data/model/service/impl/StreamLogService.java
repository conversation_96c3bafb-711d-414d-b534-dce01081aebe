package com.trs.ai.moye.data.model.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.model.request.ProcessRetryRequest;
import com.trs.ai.moye.data.model.request.StreamProcessDataListRequest;
import com.trs.ai.moye.data.model.response.DataProcessRecordMsgResponse;
import com.trs.ai.moye.data.model.response.DataProcessRecordResponse;
import com.trs.ai.moye.data.model.response.ProcessFlowResponse;
import com.trs.ai.moye.data.model.response.RerunMsgCount;
import com.trs.ai.moye.data.model.response.StorageEngineResponse;
import com.trs.ai.moye.data.model.task.start.StreamProcessHelper;
import com.trs.ai.moye.monitor.dao.DataProcessRecordMapper;
import com.trs.ai.moye.storageengine.feign.MqConnectionFeign;
import com.trs.ai.moye.streamengine.feign.StreamEngineFeign;
import com.trs.moye.ability.domain.DataProcessRecord;
import com.trs.moye.ability.domain.DataProcessTrace;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.AbilityExecuteParams;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.entity.Schema.ObjectTypeSchema;
import com.trs.moye.base.common.enums.TaskStatus;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.DataProcessUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.service.entity.ValueObject;
import com.trs.moye.base.data.storage.DataStorage;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 流处理日志service
 *
 * <AUTHOR>
 * @since 2025/8/6 14:23
 */
@Service
@Slf4j
public class StreamLogService {

    @Resource
    private DataProcessRecordMapper dataProcessRecordMapper;

    @Resource
    private StreamProcessHelper streamProcessHelper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private MqConnectionFeign mqConnectionFeign;

    @Resource
    private AbilityMapper abilityMapper;

    @Resource
    private StreamEngineFeign streamEngineFeign;

    /**
     * 重跑异常数据
     *
     * @param dataModelId     数据模型ID
     * @param timeRangeParams 时间范围
     */
    public void rerun(Integer dataModelId, TimeRangeParams timeRangeParams) {
        // TODO 兼容数据量特别大的情况
        List<String> msg = dataProcessRecordMapper.distinctSelectErrorMsgByTime(
                timeRangeParams.getBeginTime(), timeRangeParams.getEndTime(), dataModelId)
            .stream().map(DataProcessRecordMsgResponse::getMsg).filter(StringUtils::isNotBlank).toList();
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        sendMsgToRerunTopic(msg, dataModel);
        streamProcessHelper.updateRerunTaskToEtcd(dataModel);
    }

    private void sendMsgToRerunTopic(List<String> messages, DataModel dataModel) {
        DataStorage dataStorage = streamProcessHelper.getStreamProcessSourceModelStoragePoint(dataModel);
        Integer connectionId = dataStorage.getConnectionId();
        String topicName = dataStorage.getEnName() + DataProcessUtils.RERUN_SUFFIX;
        StorageEngineResponse response = mqConnectionFeign.createTopic(connectionId, topicName);
        if (response.isSuccess()) {
            mqConnectionFeign.sendMessage(connectionId, topicName, messages);
        } else {
            log.error("Failed to create topic for rerun: {}, connectionId: {}, topicName: {}", response.getMessage(),
                connectionId, topicName);
            throw new BizException("创建重跑topic失败: " + response.getMessage());
        }
    }

    /**
     * 获取异常数据数量
     *
     * @param dataModelId     数据模型ID
     * @param timeRangeParams 时间范围
     * @return 异常数据数量
     */
    public RerunMsgCount getRerunMsgCount(Integer dataModelId, TimeRangeParams timeRangeParams) {
        long totalCount = dataProcessRecordMapper.countErrorMsgByTime(timeRangeParams.getBeginTime(),
            timeRangeParams.getEndTime(), dataModelId);
        long rerunCount = dataProcessRecordMapper.distinctSelectErrorMsgByTime(
                timeRangeParams.getBeginTime(), timeRangeParams.getEndTime(), dataModelId)
            .stream().filter(record -> StringUtils.isNotBlank(record.getMsg())).count();
        return new RerunMsgCount(totalCount, rerunCount);
    }

    /**
     * 获取流处理日志列表
     *
     * @param request 请求参数
     * @return 日志
     */
    public PageResponse<DataProcessRecordResponse> getStreamProcessDataList(StreamProcessDataListRequest request) {
        PageParams pageParams = request.getPageParams();
        TimeRangeParams timeRangeParams = request.getTimeRangeParams();
        String beginTime = timeRangeParams.getMinTimeStr();
        String endTime = timeRangeParams.getMaxTimeStr();
        String operator = null;
        if (request.getOperator() != null) {
            operator = request.getOperator().getOperator();
        }
        String conditionKey = request.getConditionKey();
        Object conditionValue = request.getConditionValue();
        Integer modelId = request.getModelId();
        Boolean isError = request.getIsError();
        SearchParams searchParams = request.getSearchParams();
        Page<DataProcessRecordResponse> dataProcessRecordPage = dataProcessRecordMapper.selectStreamTaskMonitorList(
            beginTime, endTime, operator, conditionValue, conditionKey, modelId, isError, pageParams.toPage(),
            searchParams);
        return PageResponse.of(dataProcessRecordPage);
    }

    /**
     * 获取全链路日志
     *
     * @param dataModelId 数据模型ID
     * @param recordId    记录ID
     * @return 全链路日志列表
     */
    public List<ProcessFlowResponse> getDataProcessTraces(Integer dataModelId, String recordId) {
        List<DataProcessRecord> records = dataProcessRecordMapper.selectByRecordId(recordId, dataModelId);
        List<DataProcessTrace> traces = dataProcessRecordMapper.selectTracerDataListByRecordId(dataModelId, recordId);
        List<Ability> abilities = abilityMapper.selectList(null);

        List<ProcessFlowResponse> results = records.stream().map(record -> {
            ProcessFlowResponse response = new ProcessFlowResponse(record);
            List<ProcessFlowResponse> childrenTraces = traces.stream()
                .filter(trace -> trace.getExecuteId() == record.getExecuteId())
                .map(trace -> {
                    String abilityName = abilities.stream()
                        .filter(ability -> ability.getId().equals(trace.getAbilityId()))
                        .findFirst().map(Ability::getZhName).orElse("");
                    return new ProcessFlowResponse(trace, abilityName);
                })
                .sorted(Comparator.comparing(ProcessFlowResponse::getStartTime))
                .toList();
            response.setChildren(childrenTraces);
            return response;
        }).sorted(Comparator.comparing(ProcessFlowResponse::getStartTime)).toList();

        for (int i = 0; i < results.size(); i++) {
            ProcessFlowResponse response = results.get(i);
            // 不是第一次执行且不是重跑，则是重复消费
            if (i > 0 && response.getIsRerun() == 0) {
                response.setIsDuplicate(1);
            }
        }

        return results;
    }

    /**
     * 算子重试
     *
     * @param request 参数
     * @return 结果
     */
    public ProcessFlowResponse retryProcess(ProcessRetryRequest request) {
        ProcessFlowResponse response = new ProcessFlowResponse();
        response.setStartTime(LocalDateTime.now());
        long start = System.currentTimeMillis();

        // 请求服务
        AbilityExecuteParams params = createAbilityExecuteParams(request.getAbilityId(), request.getOrigin());
        ResponseMessage responseMessage = streamEngineFeign.abilityExecute(params);

        response.setDuration(System.currentTimeMillis() - start);
        response.setOrigin(request.getOrigin());

        if (Objects.isNull(responseMessage)) {
            response.setIsException(TaskStatus.FAILED);
            response.setException("stream-engine返回结果为空！");
            return response;
        } else if (!responseMessage.isSuccess()) {
            response.setIsException(TaskStatus.FAILED);
            response.setException(responseMessage.getMessage());
            return response;
        } else {
            response.setIsException(TaskStatus.SUCCESS);
            ResponseMessage data = JsonUtils.parseObject(
                JsonUtils.toJsonString(responseMessage.getData()), ResponseMessage.class);
            response.setResult(ProcessFlowResponse.toValues(JsonUtils.toJsonString(data.getData())));
        }

        return response;
    }

    private AbilityExecuteParams createAbilityExecuteParams(Integer abilityId, List<ValueObject> origin) {
        AbilityExecuteParams params = new AbilityExecuteParams();
        params.setAbilityId(abilityId);
        Ability ability = abilityMapper.selectById(abilityId);
        ObjectNode paramsNode = JsonUtils.emptyNode();
        InputBind inputBind = new InputBind();
        Schema schema = ability.getInputSchema();
        // 将所有输入以PropertyBinding的形式绑定
        if (schema instanceof ObjectTypeSchema objectTypeSchema) {
            for (Map.Entry<String, Schema> property : objectTypeSchema.getProperties().entrySet()) {
                String fieldName = property.getKey();
                for (ValueObject valueObject : origin) {
                    if (valueObject.getKey().contains(fieldName)) {
                        inputBind.addBinding(fieldName, "/" + fieldName);
                        paramsNode.put(fieldName, valueObject.getValue());
                    }
                }
            }
        }
        params.setInput(paramsNode);
        params.setInputBind(inputBind);
        return params;
    }

}

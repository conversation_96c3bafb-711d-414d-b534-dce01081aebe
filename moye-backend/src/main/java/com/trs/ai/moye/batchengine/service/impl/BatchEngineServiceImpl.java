package com.trs.ai.moye.batchengine.service.impl;

import com.trs.ai.moye.backstage.dao.AuthCertificateKerberosMapper;
import com.trs.ai.moye.backstage.entity.AuthCertificateKerberos;
import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.ai.moye.batchengine.entity.ExecuteResultMap;
import com.trs.ai.moye.batchengine.entity.ExecuteResultRequest;
import com.trs.ai.moye.batchengine.feign.BatchEngineFeignService;
import com.trs.ai.moye.batchengine.response.CodeFormatterResponse;
import com.trs.ai.moye.batchengine.service.BatchEngineService;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import com.trs.ai.moye.data.model.request.CodeFormatterRequest;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.monitor.entity.spark.SparkResourceStatus;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 批处理引擎服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/23 15:25
 **/
@Service
@Slf4j
public class BatchEngineServiceImpl implements BatchEngineService {

    @Resource
    private BatchEngineFeignService batchEngineFeignService;

    @Resource
    private AuthCertificateKerberosMapper authCertificateKerberosMapper;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;


    @Override
    public Boolean executeCode(List<BatchEngineTaskParam> tasks) {
        if (tasks.isEmpty()) {
            return false;
        }
        try {
            batchEngineFeignService.execute(tasks);
        } catch (Exception e) {
            log.error("执行批处理引擎任务失败!任务参数：{}", tasks, e);
            throw new BizException("执行批处理引擎任务失败!", e);
        }
        return true;
    }

    @Override
    public Boolean executeJava(BatchEngineTaskParam task) {
        try {
            batchEngineFeignService.dagExecute(task);
        } catch (Exception e) {
            log.error("立即执行批处理dag任务失败！任务参数：{}", task, e);
            throw new BizException("立即执行批处理dag任务失败！", e);
        }
        return true;
    }

    /**
     * 测试dag模式任务
     *
     * @param task 任务参数
     * @return 是否成功
     */
    @Override
    public List<BatchTaskTracer> testJava(BatchEngineTaskParam task) {
        try {
            return batchEngineFeignService.dagTest(task);
        } catch (Exception e) {
            log.error("测试批处理dag任务失败！任务参数：{}", task, e);
            throw new BizException("测试批处理dag任务失败！", e);
        }
    }

    @Override
    public String formatCode(CodeFormatterRequest request) {
        try {
            CodeFormatterResponse response = batchEngineFeignService.formatCode(request);
            if (Objects.nonNull(response)) {
                return response.getCode();
            } else {
                throw new BizException("响应结果为空，代码格式化失败！");
            }
        } catch (Exception e) {
            log.error("代码格式化失败！参数：{}", request, e);
            throw new BizException("代码格式化失败！", e);
        }
    }

    @Override
    public List<ExecuteResultMap> executeResult(ExecuteResultRequest executeResultRequest) {
        return batchEngineFeignService.executeResult(executeResultRequest);
    }

    @Override
    public void killTask(String executeId) {
        batchEngineFeignService.kill(executeId);
    }


    /**
     * 刷新日志
     *
     * @param executeId 执行id
     */
    @Override
    public void flushBatchLogs(String executeId) {
        batchEngineFeignService.flush(executeId);
    }

    /**
     * 获取集群资源状态
     *
     * @param dataModelId 数据模型ID
     * @return 集群资源状态
     */
    @Override
    public SparkResourceStatus getClusterResourceStatus(Integer dataModelId) {
        try {
            // 从数据模型执行配置中获取认证信息
            String principal = null;
            String keytabPath = null;

            // 查询数据模型执行配置
            DataModelExecuteConfig executeConfig = dataModelExecuteConfigMapper.selectByDataModelId(dataModelId);
            if (Objects.nonNull(executeConfig) && Objects.nonNull(executeConfig.getSparkConfig())) {
                Integer certificateId = executeConfig.getSparkConfig().getCertificateId();
                if (Objects.nonNull(certificateId)) {
                    // 直接查询数据库获取认证信息
                    AuthCertificateKerberos authCertificate = authCertificateKerberosMapper.selectById(certificateId);
                    if (Objects.nonNull(authCertificate)) {
                        principal = authCertificate.getPrincipal();
                        keytabPath = authCertificate.getKeytabPath();
                        log.info("成功获取认证信息 [dataModelId:{}, certificateId:{}, principal:{}]", dataModelId,
                            certificateId, principal);
                    }
                }
            }

            log.info("从执行配置中获取认证信息 [dataModelId:{}, authProvided:{}]", dataModelId,
                StringUtils.hasText(principal) && StringUtils.hasText(keytabPath));

            // 通过Feign调用获取集群资源状态
            ResponseMessage responseMessage = batchEngineFeignService.getClusterResourceStatus(principal, keytabPath);
            if (Objects.nonNull(responseMessage) && responseMessage.isSuccess()) {
                // 从ResponseMessage中提取ClusterResourceStatus数据
                return JsonUtils.parseObject(JsonUtils.toJsonString(responseMessage.getData()),
                    SparkResourceStatus.class);
            } else {
                String errorMsg =
                    Objects.nonNull(responseMessage) ? responseMessage.getMessage() : "获取集群资源状态失败: 响应为空";
                throw new BizException("获取集群资源状态失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("获取集群资源状态异常 [dataModelId:{}]", dataModelId, e);
            throw new BizException("获取集群资源状态失败: " + e.getMessage(), e);
        }
    }
}

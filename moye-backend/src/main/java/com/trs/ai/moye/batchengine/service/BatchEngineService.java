package com.trs.ai.moye.batchengine.service;

import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.ai.moye.batchengine.entity.ExecuteResultMap;
import com.trs.ai.moye.batchengine.entity.ExecuteResultRequest;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import com.trs.ai.moye.data.model.request.CodeFormatterRequest;
import com.trs.moye.base.monitor.entity.spark.SparkResourceStatus;
import java.util.List;

/**
 * 批处理引擎服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/23 15:25
 **/
public interface BatchEngineService {


    /**
     * 执行代码任务
     *
     * @param tasks 任务
     * @return {@link  Boolean}
     * <AUTHOR>
     * @since 2024/10/23 15:30
     */
    Boolean executeCode(List<BatchEngineTaskParam> tasks);

    /**
     * 立即执行dag任务
     *
     * @param task 任务参数
     * @return 是否成功
     */
    Boolean executeJava(BatchEngineTaskParam task);

    /**
     * 测试dag模式任务
     *
     * @param task 任务参数
     * @return 是否成功
     */
    List<BatchTaskTracer> testJava(BatchEngineTaskParam task);

    /**
     * 代码格式化
     *
     * @param request 请求
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/11/5 14:12
     */
    String formatCode(CodeFormatterRequest request);

    /**
     * 获取执行结果
     *
     * @param executeResultRequest 请求参数
     * @return 执行展示结果
     */
    List<ExecuteResultMap> executeResult(ExecuteResultRequest executeResultRequest);

    /**
     * 停止 spark 任务
     *
     * @param executeId 执行id
     */
    void killTask(String executeId);

    /**
     * 刷新日志
     *
     * @param executeId 执行id
     */
    void flushBatchLogs(String executeId);

    /**
     * 获取集群资源状态
     *
     * @param dataModelId 数据模型ID
     * @return 集群资源状态
     */
    SparkResourceStatus getClusterResourceStatus(Integer dataModelId);
}
